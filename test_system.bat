@echo off
chcp 65001 >nul
title اختبار النظام - System Test

echo ================================================
echo    اختبار نظام إدارة فواتير الكهرباء
echo    Testing Electricity Bills Management System
echo ================================================
echo.

echo فحص المتطلبات الأساسية...
echo Checking basic requirements...
echo.

REM فحص وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت
    echo ❌ Python is not installed
    pause
    exit /b 1
)

echo ✅ Python متوفر
python --version
echo.

REM فحص وجود الملفات الأساسية
echo فحص الملفات الأساسية...
echo Checking essential files...
echo.

if not exist "main.py" (
    echo ❌ main.py غير موجود
    echo ❌ main.py not found
    pause
    exit /b 1
)
echo ✅ main.py موجود

if not exist "electricity_bills.db" (
    echo ❌ electricity_bills.db غير موجود
    echo ❌ electricity_bills.db not found
    pause
    exit /b 1
)
echo ✅ electricity_bills.db موجود

if not exist "database\db_manager.py" (
    echo ❌ database\db_manager.py غير موجود
    echo ❌ database\db_manager.py not found
    pause
    exit /b 1
)
echo ✅ database\db_manager.py موجود

if not exist "gui\main_window.py" (
    echo ❌ gui\main_window.py غير موجود
    echo ❌ gui\main_window.py not found
    pause
    exit /b 1
)
echo ✅ gui\main_window.py موجود

if not exist "utils\language.py" (
    echo ❌ utils\language.py غير موجود
    echo ❌ utils\language.py not found
    pause
    exit /b 1
)
echo ✅ utils\language.py موجود

echo.
echo جميع الملفات الأساسية موجودة ✅
echo All essential files found ✅
echo.

echo ================================================
echo تشغيل الاختبارات...
echo Running tests...
echo ================================================
echo.

REM تشغيل اختبارات النظام
python test_app.py

REM فحص نتيجة الاختبارات
if errorlevel 1 (
    echo.
    echo ================================================
    echo ❌ فشلت بعض الاختبارات
    echo ❌ Some tests failed
    echo ================================================
    echo.
    echo يرجى مراجعة الأخطاء أعلاه وإصلاحها قبل استخدام النظام
    echo Please review the errors above and fix them before using the system
    echo.
) else (
    echo.
    echo ================================================
    echo ✅ جميع الاختبارات نجحت!
    echo ✅ All tests passed!
    echo ================================================
    echo.
    echo النظام جاهز للاستخدام
    echo System is ready to use
    echo.
    echo يمكنك الآن تشغيل التطبيق باستخدام:
    echo You can now run the application using:
    echo.
    echo   python main.py
    echo.
    echo أو:
    echo Or:
    echo.
    echo   run_app.bat
    echo.
)

pause
