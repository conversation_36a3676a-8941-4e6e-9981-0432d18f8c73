import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import sys
import os

# إضافة المسار الجذر للمشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.helpers import (create_treeview_with_scrollbars, format_date, format_currency, 
                          get_category_name, get_status_name, show_message, export_to_csv)

class DatabaseView:
    def __init__(self, parent, db_manager, language_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.language_manager = language_manager
        self.current_data = []
        
        # مسح المحتوى السابق
        for widget in self.parent.winfo_children():
            widget.destroy()
        
        self.create_interface()
        self.load_data()
    
    def create_interface(self):
        """إنشاء واجهة عرض قاعدة البيانات"""
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.parent)
        main_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # العنوان
        title_label = ttk.Label(main_frame, 
                               text=self.language_manager.get_text('database_view'),
                               font=('Arial', 14, 'bold'))
        title_label.pack(pady=(0, 10))
        
        # إطار أدوات التحكم
        self.create_control_panel(main_frame)
        
        # إطار الجدول
        self.create_data_table(main_frame)
        
        # إطار معلومات الإحصائيات
        self.create_stats_panel(main_frame)
    
    def create_control_panel(self, parent):
        """إنشاء لوحة التحكم"""
        control_frame = ttk.LabelFrame(parent, text="أدوات التحكم", padding="10")
        control_frame.pack(fill='x', pady=(0, 10))
        
        # الصف الأول - البحث
        search_frame = ttk.Frame(control_frame)
        search_frame.pack(fill='x', pady=(0, 10))
        
        ttk.Label(search_frame, text="البحث:").pack(side='left', padx=(0, 5))
        
        self.search_var = tk.StringVar()
        search_entry = ttk.Entry(search_frame, textvariable=self.search_var, width=30)
        search_entry.pack(side='left', padx=(0, 10))
        search_entry.bind('<KeyRelease>', self.on_search_change)
        
        ttk.Button(search_frame, text="🔍 بحث", 
                  command=self.search_data).pack(side='left', padx=(0, 5))
        ttk.Button(search_frame, text="🔄 تحديث", 
                  command=self.load_data).pack(side='left', padx=(0, 5))
        ttk.Button(search_frame, text="📤 تصدير", 
                  command=self.export_data).pack(side='left')
        
        # الصف الثاني - الفلاتر
        filter_frame = ttk.Frame(control_frame)
        filter_frame.pack(fill='x')
        
        # فلتر الصنف
        ttk.Label(filter_frame, text="الصنف:").pack(side='left', padx=(0, 5))
        self.category_var = tk.StringVar(value="الكل")
        category_combo = ttk.Combobox(filter_frame, textvariable=self.category_var, 
                                     values=["الكل", "منزلي", "تجاري", "صناعي", "حكومي"],
                                     state="readonly", width=15)
        category_combo.pack(side='left', padx=(0, 10))
        category_combo.bind('<<ComboboxSelected>>', self.apply_filters)
        
        # فلتر الحالة
        ttk.Label(filter_frame, text="الحالة:").pack(side='left', padx=(0, 5))
        self.status_var = tk.StringVar(value="الكل")
        status_combo = ttk.Combobox(filter_frame, textvariable=self.status_var,
                                   values=["الكل", "مفتوح", "مغلق"],
                                   state="readonly", width=15)
        status_combo.pack(side='left', padx=(0, 10))
        status_combo.bind('<<ComboboxSelected>>', self.apply_filters)
        
        # فلتر الديون
        ttk.Label(filter_frame, text="الديون:").pack(side='left', padx=(0, 5))
        self.debt_var = tk.StringVar(value="الكل")
        debt_combo = ttk.Combobox(filter_frame, textvariable=self.debt_var,
                                 values=["الكل", "بدون ديون", "مع ديون", "ديون عالية"],
                                 state="readonly", width=15)
        debt_combo.pack(side='left', padx=(0, 10))
        debt_combo.bind('<<ComboboxSelected>>', self.apply_filters)
        
        # زر إعادة تعيين الفلاتر
        ttk.Button(filter_frame, text="🔄 إعادة تعيين", 
                  command=self.reset_filters).pack(side='right')
    
    def create_data_table(self, parent):
        """إنشاء جدول البيانات"""
        table_frame = ttk.LabelFrame(parent, text="بيانات المشتركين", padding="5")
        table_frame.pack(fill='both', expand=True, pady=(0, 10))
        
        # تعريف الأعمدة
        columns = ['ACCTNO', 'NAME_A', 'ADRESS', 'METER_NO', 'OUTS', 'HOUSE_CODE', 'EVEN_CLOSE']
        headings = ['رقم الحساب', 'الاسم', 'العنوان', 'رقم المقياس', 'الدين', 'الصنف', 'الحالة']
        
        # إنشاء الجدول مع أشرطة التمرير
        self.tree_frame, self.tree = create_treeview_with_scrollbars(table_frame, columns, headings)
        self.tree_frame.pack(fill='both', expand=True)
        
        # تكوين عرض الأعمدة
        self.tree.column('ACCTNO', width=120, anchor='center')
        self.tree.column('NAME_A', width=200, anchor='center')
        self.tree.column('ADRESS', width=150, anchor='center')
        self.tree.column('METER_NO', width=120, anchor='center')
        self.tree.column('OUTS', width=100, anchor='center')
        self.tree.column('HOUSE_CODE', width=80, anchor='center')
        self.tree.column('EVEN_CLOSE', width=80, anchor='center')
        
        # ربط الأحداث
        self.tree.bind('<Double-1>', self.on_item_double_click)
        self.tree.bind('<Button-3>', self.show_context_menu)
    
    def create_stats_panel(self, parent):
        """إنشاء لوحة الإحصائيات"""
        stats_frame = ttk.LabelFrame(parent, text="إحصائيات", padding="10")
        stats_frame.pack(fill='x')
        
        # إطار للإحصائيات
        self.stats_labels_frame = ttk.Frame(stats_frame)
        self.stats_labels_frame.pack(fill='x')
        
        # تحديث الإحصائيات
        self.update_stats()
    
    def load_data(self):
        """تحميل البيانات من قاعدة البيانات"""
        try:
            self.current_data = self.db_manager.get_all_bills(limit=5000)
            self.display_data(self.current_data)
            self.update_stats()
        except Exception as e:
            show_message("خطأ", f"فشل في تحميل البيانات: {str(e)}", "error")
    
    def display_data(self, data):
        """عرض البيانات في الجدول"""
        # مسح البيانات السابقة
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # إضافة البيانات الجديدة
        for row in data:
            values = [
                str(row.get('ACCTNO', '')),
                str(row.get('NAME_A', '')),
                str(row.get('ADRESS', '')),
                str(row.get('METER_NO', '')),
                format_currency(row.get('OUTS', 0)),
                get_category_name(row.get('HOUSE_CODE', 0), self.language_manager.get_current_language()),
                get_status_name(row.get('EVEN_CLOSE', 0), self.language_manager.get_current_language())
            ]
            self.tree.insert('', 'end', values=values)
    
    def on_search_change(self, event):
        """عند تغيير نص البحث"""
        # البحث التلقائي بعد توقف الكتابة
        self.parent.after(500, self.search_data)
    
    def search_data(self):
        """البحث في البيانات"""
        search_term = self.search_var.get().strip()
        
        if not search_term:
            self.display_data(self.current_data)
            return
        
        try:
            # البحث المتعدد
            results = self.db_manager.multi_search(search_term)
            self.display_data(results)
            self.update_stats(len(results))
        except Exception as e:
            show_message("خطأ", f"فشل في البحث: {str(e)}", "error")
    
    def apply_filters(self, event=None):
        """تطبيق الفلاتر"""
        try:
            filtered_data = self.current_data.copy()
            
            # فلتر الصنف
            category = self.category_var.get()
            if category != "الكل":
                category_codes = {"منزلي": 15, "تجاري": 96, "صناعي": 97, "حكومي": 98}
                if category in category_codes:
                    filtered_data = [row for row in filtered_data 
                                   if row.get('HOUSE_CODE') == category_codes[category]]
            
            # فلتر الحالة
            status = self.status_var.get()
            if status != "الكل":
                status_code = 0 if status == "مفتوح" else 1
                filtered_data = [row for row in filtered_data 
                               if row.get('EVEN_CLOSE') == status_code]
            
            # فلتر الديون
            debt = self.debt_var.get()
            if debt != "الكل":
                if debt == "بدون ديون":
                    filtered_data = [row for row in filtered_data if row.get('OUTS', 0) == 0]
                elif debt == "مع ديون":
                    filtered_data = [row for row in filtered_data if row.get('OUTS', 0) > 0]
                elif debt == "ديون عالية":
                    filtered_data = [row for row in filtered_data if row.get('OUTS', 0) > 10000]
            
            self.display_data(filtered_data)
            self.update_stats(len(filtered_data))
            
        except Exception as e:
            show_message("خطأ", f"فشل في تطبيق الفلاتر: {str(e)}", "error")

    def reset_filters(self):
        """إعادة تعيين الفلاتر"""
        self.category_var.set("الكل")
        self.status_var.set("الكل")
        self.debt_var.set("الكل")
        self.search_var.set("")
        self.display_data(self.current_data)
        self.update_stats()

    def update_stats(self, filtered_count=None):
        """تحديث الإحصائيات"""
        # مسح الإحصائيات السابقة
        for widget in self.stats_labels_frame.winfo_children():
            widget.destroy()

        try:
            data_to_analyze = self.current_data
            total_count = len(data_to_analyze)

            if filtered_count is not None:
                display_count = filtered_count
            else:
                display_count = total_count

            # حساب الإحصائيات
            total_debt = sum(row.get('OUTS', 0) for row in data_to_analyze)
            closed_count = sum(1 for row in data_to_analyze if row.get('EVEN_CLOSE', 0) == 1)
            commercial_count = sum(1 for row in data_to_analyze if row.get('HOUSE_CODE', 0) == 96)
            residential_count = sum(1 for row in data_to_analyze if row.get('HOUSE_CODE', 0) == 15)

            # عرض الإحصائيات
            stats_data = [
                ("عدد السجلات المعروضة", display_count),
                ("إجمالي السجلات", total_count),
                ("إجمالي الديون", f"{total_debt:,.0f}"),
                ("الحسابات المغلقة", closed_count),
                ("الحسابات التجارية", commercial_count),
                ("الحسابات المنزلية", residential_count)
            ]

            # ترتيب الإحصائيات في صفوف
            for i, (label, value) in enumerate(stats_data):
                row = i // 3
                col = (i % 3) * 2

                ttk.Label(self.stats_labels_frame, text=f"{label}:",
                         font=('Arial', 9)).grid(row=row, column=col, sticky='w', padx=(0, 5), pady=2)
                ttk.Label(self.stats_labels_frame, text=str(value),
                         font=('Arial', 9, 'bold')).grid(row=row, column=col+1, sticky='w', padx=(0, 20), pady=2)

        except Exception as e:
            ttk.Label(self.stats_labels_frame, text="خطأ في حساب الإحصائيات",
                     font=('Arial', 9)).grid(row=0, column=0)

    def on_item_double_click(self, event):
        """عند النقر المزدوج على عنصر"""
        selection = self.tree.selection()
        if selection:
            item = self.tree.item(selection[0])
            values = item['values']

            # عرض تفاصيل المشترك
            self.show_customer_details(values[0])  # رقم الحساب

    def show_context_menu(self, event):
        """عرض القائمة السياقية"""
        selection = self.tree.selection()
        if not selection:
            return

        context_menu = tk.Menu(self.tree, tearoff=0)
        context_menu.add_command(label="عرض التفاصيل", command=lambda: self.show_customer_details(
            self.tree.item(selection[0])['values'][0]))
        context_menu.add_command(label="نسخ رقم الحساب", command=lambda: self.copy_to_clipboard(
            self.tree.item(selection[0])['values'][0]))
        context_menu.add_separator()
        context_menu.add_command(label="تصدير هذا السجل", command=lambda: self.export_single_record(
            self.tree.item(selection[0])['values'][0]))

        try:
            context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            context_menu.grab_release()

    def show_customer_details(self, account_no):
        """عرض تفاصيل المشترك"""
        try:
            # البحث عن المشترك
            results = self.db_manager.search_by_account(str(account_no))
            if not results:
                show_message("خطأ", "لم يتم العثور على المشترك", "error")
                return

            customer = results[0]

            # إنشاء نافذة التفاصيل
            details_window = tk.Toplevel(self.parent)
            details_window.title(f"تفاصيل المشترك - {customer.get('NAME_A', '')}")
            details_window.geometry("600x500")
            details_window.resizable(False, False)

            # إطار التفاصيل
            details_frame = ttk.Frame(details_window, padding="20")
            details_frame.pack(fill='both', expand=True)

            # العنوان
            title_label = ttk.Label(details_frame, text="تفاصيل المشترك",
                                   font=('Arial', 14, 'bold'))
            title_label.pack(pady=(0, 20))

            # البيانات الأساسية
            basic_frame = ttk.LabelFrame(details_frame, text="البيانات الأساسية", padding="10")
            basic_frame.pack(fill='x', pady=(0, 10))

            basic_data = [
                ("رقم الحساب", customer.get('ACCTNO', '')),
                ("الاسم", customer.get('NAME_A', '')),
                ("العنوان", customer.get('ADRESS', '')),
                ("رقم المنزل", customer.get('HOUSE_NO', '')),
                ("رقم المقياس", customer.get('METER_NO', '')),
                ("عدد الأطوار", customer.get('MPHASE', '')),
                ("معامل المقياس", customer.get('METER_FACT', ''))
            ]

            for i, (label, value) in enumerate(basic_data):
                ttk.Label(basic_frame, text=f"{label}:").grid(row=i, column=0, sticky='w', pady=2)
                ttk.Label(basic_frame, text=str(value), font=('Arial', 9, 'bold')).grid(
                    row=i, column=1, sticky='w', padx=(20, 0), pady=2)

            # بيانات القراءات
            readings_frame = ttk.LabelFrame(details_frame, text="بيانات القراءات", padding="10")
            readings_frame.pack(fill='x', pady=(0, 10))

            readings_data = [
                ("القراءة الحالية", customer.get('LAST_READ', 0)),
                ("تاريخ القراءة الحالية", format_date(customer.get('LAST_DATE', 0))),
                ("القراءة السابقة", customer.get('PREV_READ', 0)),
                ("تاريخ القراءة السابقة", format_date(customer.get('PREV_DATE', 0))),
                ("الاستهلاك", customer.get('LAST_READ', 0) - customer.get('PREV_READ', 0))
            ]

            for i, (label, value) in enumerate(readings_data):
                ttk.Label(readings_frame, text=f"{label}:").grid(row=i, column=0, sticky='w', pady=2)
                ttk.Label(readings_frame, text=str(value), font=('Arial', 9, 'bold')).grid(
                    row=i, column=1, sticky='w', padx=(20, 0), pady=2)

            # البيانات المالية
            financial_frame = ttk.LabelFrame(details_frame, text="البيانات المالية", padding="10")
            financial_frame.pack(fill='x', pady=(0, 10))

            financial_data = [
                ("الدين الحالي", format_currency(customer.get('OUTS', 0))),
                ("الدين السابق", format_currency(customer.get('BKOUTS', 0))),
                ("المدفوع", format_currency(customer.get('PAYMENT', 0))),
                ("تاريخ الدفع", format_date(customer.get('PAY_DATE', 0))),
                ("تاريخ الفاتورة", format_date(customer.get('BILL_DATE', 0)))
            ]

            for i, (label, value) in enumerate(financial_data):
                ttk.Label(financial_frame, text=f"{label}:").grid(row=i, column=0, sticky='w', pady=2)
                ttk.Label(financial_frame, text=str(value), font=('Arial', 9, 'bold')).grid(
                    row=i, column=1, sticky='w', padx=(20, 0), pady=2)

            # معلومات إضافية
            additional_frame = ttk.LabelFrame(details_frame, text="معلومات إضافية", padding="10")
            additional_frame.pack(fill='x', pady=(0, 20))

            additional_data = [
                ("الصنف", get_category_name(customer.get('HOUSE_CODE', 0))),
                ("الحالة", get_status_name(customer.get('EVEN_CLOSE', 0))),
                ("أجرة المقياس", customer.get('METER_RENT', '')),
                ("أجرة القاطع", customer.get('CB_RENT', ''))
            ]

            for i, (label, value) in enumerate(additional_data):
                ttk.Label(additional_frame, text=f"{label}:").grid(row=i, column=0, sticky='w', pady=2)
                ttk.Label(additional_frame, text=str(value), font=('Arial', 9, 'bold')).grid(
                    row=i, column=1, sticky='w', padx=(20, 0), pady=2)

            # زر الإغلاق
            ttk.Button(details_frame, text="إغلاق",
                      command=details_window.destroy).pack(pady=10)

        except Exception as e:
            show_message("خطأ", f"فشل في عرض التفاصيل: {str(e)}", "error")

    def copy_to_clipboard(self, text):
        """نسخ النص إلى الحافظة"""
        self.parent.clipboard_clear()
        self.parent.clipboard_append(str(text))
        show_message("نجح", "تم نسخ النص إلى الحافظة", "info")

    def export_data(self):
        """تصدير البيانات"""
        if not self.current_data:
            show_message("تحذير", "لا توجد بيانات للتصدير", "warning")
            return

        try:
            filename = filedialog.asksaveasfilename(
                defaultextension=".csv",
                filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
                title="حفظ البيانات"
            )

            if filename:
                headers = ['ACCTNO', 'NAME_A', 'ADRESS', 'METER_NO', 'OUTS', 'HOUSE_CODE', 'EVEN_CLOSE']
                if export_to_csv(self.current_data, filename, headers):
                    show_message("نجح", f"تم تصدير البيانات إلى {filename}", "info")
                else:
                    show_message("خطأ", "فشل في تصدير البيانات", "error")

        except Exception as e:
            show_message("خطأ", f"فشل في التصدير: {str(e)}", "error")

    def export_single_record(self, account_no):
        """تصدير سجل واحد"""
        try:
            results = self.db_manager.search_by_account(str(account_no))
            if not results:
                show_message("خطأ", "لم يتم العثور على السجل", "error")
                return

            filename = filedialog.asksaveasfilename(
                defaultextension=".csv",
                filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
                title="حفظ السجل"
            )

            if filename:
                headers = list(results[0].keys())
                if export_to_csv(results, filename, headers):
                    show_message("نجح", f"تم تصدير السجل إلى {filename}", "info")
                else:
                    show_message("خطأ", "فشل في تصدير السجل", "error")

        except Exception as e:
            show_message("خطأ", f"فشل في التصدير: {str(e)}", "error")
