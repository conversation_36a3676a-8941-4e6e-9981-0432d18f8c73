# نظام إدارة فواتير الكهرباء
## Electricity Bills Management System

نظام شامل ومتكامل لإدارة فواتير الكهرباء مع واجهة مستخدم احترافية ثنائية اللغة (العربية والإنجليزية).

### 🌟 الميزات الرئيسية

#### 1. واجهة قاعدة البيانات
- عرض جميع بيانات المشتركين في جدول منظم
- فلترة البيانات حسب الاسم، الصنف، الحالة، والديون
- البحث السريع والمتقدم
- تصدير البيانات إلى ملفات CSV
- عرض تفاصيل شاملة لكل مشترك

#### 2. واجهة الاستفسارات
- البحث المتعدد بالاسم، رقم الحساب، ورقم المقياس
- عرض نتائج البحث في جدول تفاعلي
- عرض تفاصيل المشترك في لوحة منفصلة
- نافذة تفاصيل شاملة مع إمكانية الطباعة والتصدير

#### 3. واجهة المعاملات
- استبدال المقاييس مع التحقق من صحة البيانات
- إغلاق الحسابات مع تأكيد العملية
- تغيير أسماء المشتركين
- تغيير أصناف الحسابات
- فلترة المعاملات حسب النوع والحالة

#### 4. واجهة التقارير
- تقرير الاستهلاك العالي مع تحديد الحد الأدنى
- تقرير الديون مرتب حسب المبلغ
- تقارير الحسابات التجارية والمنزلية
- تقرير الحسابات الجديدة خلال فترة محددة
- تقرير الإحصائيات العامة
- تقرير الحسابات المغلقة
- تقرير المقاييس المعطلة
- طباعة وتصدير التقارير بصيغ مختلفة (CSV, PDF, HTML)

#### 5. واجهة المقارنات
- مقارنة الاستهلاكات بين الأشهر
- تحليل الضائعات بين القراءات
- مقارنة الديون بين الفترات
- مقارنة الأصناف المختلفة
- رسوم بيانية تفاعلية
- تصدير نتائج المقارنات

### 🛠️ المتطلبات التقنية

#### متطلبات النظام
- نظام التشغيل: Windows 10/11, macOS, Linux
- Python 3.6 أو أحدث
- ذاكرة RAM: 4 جيجابايت على الأقل
- مساحة القرص الصلب: 500 ميجابايت

#### المكتبات المطلوبة
```bash
pip install -r requirements.txt
```

المكتبات الأساسية:
- `tkinter` (مدمج مع Python)
- `sqlite3` (مدمج مع Python)
- `reportlab` - لإنشاء تقارير PDF
- `matplotlib` - للرسوم البيانية
- `pandas` - لتحليل البيانات
- `numpy` - للعمليات الرياضية
- `Pillow` - لمعالجة الصور
- `openpyxl` - للتعامل مع ملفات Excel

### 📦 التثبيت والتشغيل

#### 1. تحميل المشروع
```bash
git clone [repository-url]
cd electricity-bills-management
```

#### 2. تثبيت المكتبات
```bash
pip install -r requirements.txt
```

#### 3. التأكد من وجود قاعدة البيانات
تأكد من وجود ملف `electricity_bills.db` في نفس مجلد المشروع.

#### 4. تشغيل التطبيق
```bash
python main.py
```

### 🏗️ بناء ملف تنفيذي (EXE)

لإنشاء ملف تنفيذي مستقل:

```bash
# تثبيت PyInstaller
pip install pyinstaller

# إنشاء ملف تنفيذي
pyinstaller --onefile --windowed --name="ElectricityBillsManager" main.py

# أو استخدام الأمر المفصل
pyinstaller --onefile --windowed --add-data "electricity_bills.db;." --add-data "assets;assets" --name="نظام_إدارة_فواتير_الكهرباء" main.py
```

الملف التنفيذي سيكون في مجلد `dist/`.

### 📊 هيكل قاعدة البيانات

قاعدة البيانات تحتوي على جدول `electricity_bills` بالحقول التالية:

| الحقل | النوع | الوصف |
|-------|--------|--------|
| ACCTNO | INTEGER | رقم الحساب |
| INSTAL_NO | REAL | رقم التركيب |
| SERIAL | REAL | الرقم التسلسلي |
| NAME_A | TEXT | اسم المشترك |
| HOUSE_NO | TEXT | رقم المنزل |
| ADRESS | TEXT | العنوان |
| METER_NO | REAL | رقم المقياس |
| MPHASE | INTEGER | عدد الأطوار |
| METER_FACT | INTEGER | معامل المقياس |
| LAST_READ | INTEGER | القراءة الحالية |
| LAST_DATE | INTEGER | تاريخ القراءة الحالية |
| PREV_READ | INTEGER | القراءة السابقة |
| PREV_DATE | INTEGER | تاريخ القراءة السابقة |
| METER_RENT | TEXT | أجرة المقياس |
| CB_RENT | TEXT | أجرة القاطع |
| OTHCHARGE | REAL | رسوم أخرى |
| OUTS | REAL | الدين الحالي |
| BKOUTS | REAL | الدين السابق |
| HOUSE_CODE | INTEGER | رمز الصنف (15=منزلي، 96=تجاري، 97=صناعي، 98=حكومي) |
| EVEN_CLOSE | INTEGER | حالة الحساب (0=مفتوح، 1=مغلق) |
| PAYMENT | REAL | المبلغ المدفوع |
| PAY_DATE | INTEGER | تاريخ الدفع |
| BILL_DATE | INTEGER | تاريخ الفاتورة |

### 🎨 الواجهة والتصميم

- تصميم احترافي وجميل مع ألوان متناسقة
- أيقونات ورموز تعبيرية لسهولة الاستخدام
- دعم كامل للغة العربية مع اتجاه النص من اليمين لليسار
- واجهة ثنائية اللغة (عربي/إنجليزي)
- تخطيط متجاوب يتكيف مع أحجام الشاشات المختلفة

### 🔧 الاستخدام

#### البدء السريع
1. شغل التطبيق باستخدام `python main.py`
2. ستظهر شاشة البداية مع شريط التحميل
3. بعد التحميل، ستظهر النافذة الرئيسية
4. استخدم الأزرار الجانبية للتنقل بين الواجهات المختلفة

#### نصائح الاستخدام
- استخدم البحث المتعدد للعثور على المشتركين بسرعة
- اضغط مرتين على أي سجل لعرض التفاصيل الكاملة
- استخدم الفلاتر لتضييق نطاق البحث
- صدر التقارير بانتظام للاحتفاظ بنسخ احتياطية

### 🐛 استكشاف الأخطاء

#### مشاكل شائعة وحلولها

**1. خطأ في قاعدة البيانات**
- تأكد من وجود ملف `electricity_bills.db`
- تحقق من صلاحيات القراءة والكتابة

**2. مشاكل في الرسوم البيانية**
- تأكد من تثبيت `matplotlib`
- أعد تشغيل التطبيق

**3. مشاكل في التصدير**
- تحقق من صلاحيات الكتابة في المجلد المحدد
- تأكد من عدم فتح الملف في برنامج آخر

### 📝 ملفات السجلات

التطبيق ينشئ ملفات سجلات للأخطاء:
- `error_log.txt` - سجل الأخطاء العامة
- `startup_error_log.txt` - سجل أخطاء بدء التشغيل

### 🤝 المساهمة والدعم

هذا المشروع تم تطويره بواسطة Augment Agent. للدعم الفني أو الاستفسارات، يرجى مراجعة ملفات السجلات أولاً.

### 📄 الترخيص

جميع الحقوق محفوظة © 2024

---

**ملاحظة**: هذا النظام مصمم للاستخدام المحلي ويتطلب قاعدة بيانات SQLite صالحة للعمل بشكل صحيح.
