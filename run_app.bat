@echo off
chcp 65001 >nul
title نظام إدارة فواتير الكهرباء - Electricity Bills Management System

echo ================================================
echo    نظام إدارة فواتير الكهرباء
echo    Electricity Bills Management System
echo ================================================
echo.

echo جاري فحص المتطلبات...
echo Checking requirements...
echo.

REM فحص وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: Python غير مثبت أو غير موجود في PATH
    echo Error: Python is not installed or not in PATH
    echo.
    echo يرجى تثبيت Python 3.6 أو أحدث من:
    echo Please install Python 3.6 or newer from:
    echo https://www.python.org/downloads/
    echo.
    pause
    exit /b 1
)

echo تم العثور على Python ✓
echo Python found ✓
echo.

REM فحص وجود قاعدة البيانات
if not exist "electricity_bills.db" (
    echo خطأ: ملف قاعدة البيانات غير موجود
    echo Error: Database file not found
    echo.
    echo يرجى التأكد من وجود ملف electricity_bills.db في نفس المجلد
    echo Please ensure electricity_bills.db exists in the same folder
    echo.
    pause
    exit /b 1
)

echo تم العثور على قاعدة البيانات ✓
echo Database found ✓
echo.

REM فحص وجود المكتبات المطلوبة
echo فحص المكتبات المطلوبة...
echo Checking required libraries...

python -c "import tkinter, sqlite3, datetime, csv, json" >nul 2>&1
if errorlevel 1 (
    echo خطأ: بعض المكتبات الأساسية غير متوفرة
    echo Error: Some basic libraries are missing
    echo.
    pause
    exit /b 1
)

echo المكتبات الأساسية متوفرة ✓
echo Basic libraries available ✓
echo.

REM فحص المكتبات الاختيارية
echo فحص المكتبات الاختيارية...
echo Checking optional libraries...

python -c "import matplotlib" >nul 2>&1
if errorlevel 1 (
    echo تحذير: matplotlib غير مثبت - الرسوم البيانية لن تعمل
    echo Warning: matplotlib not installed - charts will not work
)

python -c "import reportlab" >nul 2>&1
if errorlevel 1 (
    echo تحذير: reportlab غير مثبت - تصدير PDF لن يعمل
    echo Warning: reportlab not installed - PDF export will not work
)

python -c "import pandas" >nul 2>&1
if errorlevel 1 (
    echo تحذير: pandas غير مثبت - بعض ميزات التحليل لن تعمل
    echo Warning: pandas not installed - some analysis features will not work
)

echo.
echo جاري تشغيل التطبيق...
echo Starting application...
echo.

REM تشغيل التطبيق
python main.py

REM فحص حالة الخروج
if errorlevel 1 (
    echo.
    echo حدث خطأ أثناء تشغيل التطبيق
    echo An error occurred while running the application
    echo.
    echo يرجى مراجعة ملفات السجلات للمزيد من التفاصيل:
    echo Please check log files for more details:
    echo - error_log.txt
    echo - startup_error_log.txt
    echo.
) else (
    echo.
    echo تم إغلاق التطبيق بنجاح
    echo Application closed successfully
    echo.
)

pause
