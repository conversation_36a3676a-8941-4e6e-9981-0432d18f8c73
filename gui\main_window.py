import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# إضافة المسار الجذر للمشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.language import LanguageManager
from utils.helpers import center_window, show_message
from database.db_manager import DatabaseManager

class MainWindow:
    def __init__(self):
        self.root = tk.Tk()
        self.language_manager = LanguageManager()
        self.db_manager = DatabaseManager()
        
        # تكوين النافذة الرئيسية
        self.setup_main_window()
        self.create_menu()
        self.create_main_interface()
        
        # الاتصال بقاعدة البيانات
        if not self.db_manager.connect():
            show_message("خطأ", "فشل في الاتصال بقاعدة البيانات", "error")
            self.root.destroy()
            return
    
    def setup_main_window(self):
        """تكوين النافذة الرئيسية"""
        self.root.title(self.language_manager.get_text('main_title'))
        self.root.geometry("1200x800")
        center_window(self.root, 1200, 800)
        
        # تكوين الأيقونة (إذا كانت متوفرة)
        try:
            self.root.iconbitmap("assets/icons/main.ico")
        except:
            pass
        
        # تكوين الخط للعربية
        self.arabic_font = ('Arial', 12)
        self.english_font = ('Arial', 10)
        
        # تكوين الألوان
        self.colors = {
            'primary': '#2E86AB',
            'secondary': '#A23B72',
            'success': '#F18F01',
            'danger': '#C73E1D',
            'light': '#F5F5F5',
            'dark': '#333333'
        }
        
        # تكوين النمط
        self.setup_styles()
    
    def setup_styles(self):
        """تكوين أنماط ttk"""
        style = ttk.Style()
        
        # تكوين النمط العام
        style.configure('Title.TLabel', font=('Arial', 16, 'bold'))
        style.configure('Heading.TLabel', font=('Arial', 12, 'bold'))
        style.configure('Primary.TButton', font=('Arial', 10, 'bold'))
        
        # تكوين ألوان الأزرار
        style.map('Primary.TButton',
                 background=[('active', self.colors['primary']),
                           ('pressed', self.colors['secondary'])])
    
    def create_menu(self):
        """إنشاء شريط القوائم"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # قائمة الملف
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="ملف", menu=file_menu)
        file_menu.add_command(label="تصدير البيانات", command=self.export_data)
        file_menu.add_separator()
        file_menu.add_command(label=self.language_manager.get_text('exit'), command=self.on_closing)
        
        # قائمة العرض
        view_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="عرض", menu=view_menu)
        view_menu.add_command(label="تحديث البيانات", command=self.refresh_data)
        view_menu.add_command(label="إعادة تعيين الفلاتر", command=self.reset_filters)
        
        # قائمة اللغة
        language_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label=self.language_manager.get_text('language'), menu=language_menu)
        language_menu.add_command(label=self.language_manager.get_text('arabic'), 
                                command=lambda: self.change_language('ar'))
        language_menu.add_command(label=self.language_manager.get_text('english'), 
                                command=lambda: self.change_language('en'))
        
        # قائمة المساعدة
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="مساعدة", menu=help_menu)
        help_menu.add_command(label="حول البرنامج", command=self.show_about)
        help_menu.add_command(label="دليل الاستخدام", command=self.show_help)
    
    def create_main_interface(self):
        """إنشاء الواجهة الرئيسية"""
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # تكوين الشبكة
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(1, weight=1)
        
        # العنوان الرئيسي
        title_label = ttk.Label(main_frame, 
                               text=self.language_manager.get_text('main_title'),
                               style='Title.TLabel')
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # الشريط الجانبي للأزرار
        self.create_sidebar(main_frame)
        
        # المنطقة الرئيسية للمحتوى
        self.content_frame = ttk.Frame(main_frame, relief='sunken', borderwidth=2)
        self.content_frame.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(10, 0))
        self.content_frame.columnconfigure(0, weight=1)
        self.content_frame.rowconfigure(0, weight=1)
        
        # عرض الصفحة الافتراضية
        self.show_welcome_page()
        
        # شريط الحالة
        self.create_status_bar()
    
    def create_sidebar(self, parent):
        """إنشاء الشريط الجانبي"""
        sidebar_frame = ttk.Frame(parent, width=200)
        sidebar_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))
        sidebar_frame.grid_propagate(False)
        
        # أزرار التنقل
        buttons_data = [
            ('database_view', '📊 ' + self.language_manager.get_text('database_view'), self.show_database_view),
            ('inquiry', '🔍 ' + self.language_manager.get_text('inquiry'), self.show_inquiry),
            ('transactions', '💼 ' + self.language_manager.get_text('transactions'), self.show_transactions),
            ('reports', '📈 ' + self.language_manager.get_text('reports'), self.show_reports),
            ('comparison', '📊 ' + self.language_manager.get_text('comparison'), self.show_comparison),
        ]
        
        self.nav_buttons = {}
        for i, (key, text, command) in enumerate(buttons_data):
            btn = ttk.Button(sidebar_frame, text=text, command=command, 
                           style='Primary.TButton', width=25)
            btn.grid(row=i, column=0, pady=5, padx=5, sticky=(tk.W, tk.E))
            self.nav_buttons[key] = btn
        
        # فاصل
        ttk.Separator(sidebar_frame, orient='horizontal').grid(row=len(buttons_data), 
                                                              column=0, sticky=(tk.W, tk.E), 
                                                              pady=10, padx=5)
        
        # معلومات سريعة
        self.create_quick_info(sidebar_frame, len(buttons_data) + 1)
    
    def create_quick_info(self, parent, start_row):
        """إنشاء معلومات سريعة"""
        info_label = ttk.Label(parent, text="معلومات سريعة", style='Heading.TLabel')
        info_label.grid(row=start_row, column=0, pady=(10, 5))
        
        # إحصائيات سريعة
        self.stats_frame = ttk.Frame(parent)
        self.stats_frame.grid(row=start_row + 1, column=0, sticky=(tk.W, tk.E), padx=5)
        
        self.update_quick_stats()
    
    def update_quick_stats(self):
        """تحديث الإحصائيات السريعة"""
        # مسح الإحصائيات السابقة
        for widget in self.stats_frame.winfo_children():
            widget.destroy()
        
        try:
            stats = self.db_manager.get_statistics()
            
            stats_data = [
                ("إجمالي الحسابات", stats.get('total_accounts', 0)),
                ("الحسابات المغلقة", stats.get('closed_accounts', 0)),
                ("إجمالي الديون", f"{stats.get('total_debt', 0):,.0f}"),
                ("الحسابات التجارية", stats.get('commercial_accounts', 0)),
                ("الحسابات المنزلية", stats.get('residential_accounts', 0))
            ]
            
            for i, (label, value) in enumerate(stats_data):
                ttk.Label(self.stats_frame, text=f"{label}:", 
                         font=('Arial', 9)).grid(row=i, column=0, sticky=tk.W, pady=2)
                ttk.Label(self.stats_frame, text=str(value), 
                         font=('Arial', 9, 'bold')).grid(row=i, column=1, sticky=tk.E, pady=2)
        
        except Exception as e:
            ttk.Label(self.stats_frame, text="خطأ في جلب الإحصائيات", 
                     font=('Arial', 9)).grid(row=0, column=0)
    
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        self.status_bar = ttk.Frame(self.root)
        self.status_bar.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
        self.status_label = ttk.Label(self.status_bar, text="جاهز", relief='sunken')
        self.status_label.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        # معلومات الاتصال
        connection_label = ttk.Label(self.status_bar, text="متصل بقاعدة البيانات", relief='sunken')
        connection_label.pack(side=tk.RIGHT)
    
    def show_welcome_page(self):
        """عرض صفحة الترحيب"""
        # مسح المحتوى السابق
        for widget in self.content_frame.winfo_children():
            widget.destroy()
        
        welcome_frame = ttk.Frame(self.content_frame)
        welcome_frame.pack(expand=True, fill='both')
        
        # رسالة الترحيب
        welcome_label = ttk.Label(welcome_frame, 
                                 text="مرحباً بك في نظام إدارة فواتير الكهرباء",
                                 style='Title.TLabel')
        welcome_label.pack(pady=50)
        
        # وصف النظام
        description = """
        هذا النظام يوفر إدارة شاملة لفواتير الكهرباء ويشمل:
        
        • عرض وفلترة بيانات المشتركين
        • البحث المتقدم في قاعدة البيانات
        • إدارة المعاملات والتحديثات
        • إنشاء التقارير المفصلة
        • مقارنة الاستهلاكات والضائعات
        
        اختر من القائمة الجانبية للبدء
        """
        
        desc_label = ttk.Label(welcome_frame, text=description, 
                              font=('Arial', 11), justify='center')
        desc_label.pack(pady=20)
    
    def show_database_view(self):
        """عرض واجهة قاعدة البيانات"""
        from gui.database_view import DatabaseView
        self.current_view = DatabaseView(self.content_frame, self.db_manager, self.language_manager)
        self.update_status("عرض قاعدة البيانات")
    
    def show_inquiry(self):
        """عرض واجهة الاستفسار"""
        from gui.inquiry_window import InquiryWindow
        self.current_view = InquiryWindow(self.content_frame, self.db_manager, self.language_manager)
        self.update_status("واجهة الاستفسار")
    
    def show_transactions(self):
        """عرض واجهة المعاملات"""
        from gui.transactions import TransactionsWindow
        self.current_view = TransactionsWindow(self.content_frame, self.db_manager, self.language_manager)
        self.update_status("واجهة المعاملات")
    
    def show_reports(self):
        """عرض واجهة التقارير"""
        from gui.reports import ReportsWindow
        self.current_view = ReportsWindow(self.content_frame, self.db_manager, self.language_manager)
        self.update_status("واجهة التقارير")
    
    def show_comparison(self):
        """عرض واجهة المقارنات"""
        from gui.comparison import ComparisonWindow
        self.current_view = ComparisonWindow(self.content_frame, self.db_manager, self.language_manager)
        self.update_status("واجهة المقارنات")
    
    def change_language(self, language):
        """تغيير اللغة"""
        self.language_manager.set_language(language)
        self.refresh_interface()
        self.update_status(f"تم تغيير اللغة إلى {self.language_manager.get_text(language)}")
    
    def refresh_interface(self):
        """تحديث الواجهة بعد تغيير اللغة"""
        # تحديث العنوان
        self.root.title(self.language_manager.get_text('main_title'))
        
        # تحديث الأزرار
        buttons_text = {
            'database_view': '📊 ' + self.language_manager.get_text('database_view'),
            'inquiry': '🔍 ' + self.language_manager.get_text('inquiry'),
            'transactions': '💼 ' + self.language_manager.get_text('transactions'),
            'reports': '📈 ' + self.language_manager.get_text('reports'),
            'comparison': '📊 ' + self.language_manager.get_text('comparison'),
        }
        
        for key, text in buttons_text.items():
            if key in self.nav_buttons:
                self.nav_buttons[key].configure(text=text)
    
    def update_status(self, message):
        """تحديث شريط الحالة"""
        self.status_label.configure(text=message)
    
    def export_data(self):
        """تصدير البيانات"""
        show_message("تصدير", "سيتم إضافة وظيفة التصدير قريباً", "info")
    
    def refresh_data(self):
        """تحديث البيانات"""
        self.update_quick_stats()
        self.update_status("تم تحديث البيانات")
    
    def reset_filters(self):
        """إعادة تعيين الفلاتر"""
        self.update_status("تم إعادة تعيين الفلاتر")
    
    def show_about(self):
        """عرض معلومات البرنامج"""
        about_text = """
        نظام إدارة فواتير الكهرباء
        الإصدار 1.0
        
        تم تطويره باستخدام Python و Tkinter
        
        © 2024 جميع الحقوق محفوظة
        """
        show_message("حول البرنامج", about_text, "info")
    
    def show_help(self):
        """عرض دليل الاستخدام"""
        help_text = """
        دليل الاستخدام السريع:
        
        1. استخدم الأزرار الجانبية للتنقل بين الواجهات
        2. في واجهة قاعدة البيانات: يمكنك عرض وفلترة البيانات
        3. في واجهة الاستفسار: يمكنك البحث عن المشتركين
        4. في واجهة المعاملات: يمكنك إدارة المعاملات
        5. في واجهة التقارير: يمكنك إنشاء التقارير
        6. في واجهة المقارنات: يمكنك مقارنة الاستهلاكات
        """
        show_message("دليل الاستخدام", help_text, "info")
    
    def on_closing(self):
        """عند إغلاق البرنامج"""
        if show_message("تأكيد الخروج", "هل تريد إغلاق البرنامج؟", "question"):
            self.db_manager.disconnect()
            self.root.destroy()
    
    def run(self):
        """تشغيل البرنامج"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()

if __name__ == "__main__":
    app = MainWindow()
    app.run()
