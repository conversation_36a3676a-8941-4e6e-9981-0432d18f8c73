# المكتبات الأساسية المطلوبة لنظام إدارة فواتير الكهرباء
# Basic libraries required for Electricity Bills Management System

# واجهة المستخدم الرسومية - GUI Framework
# tkinter مدمج مع Python، لا يحتاج تثبيت منفصل

# قاعدة البيانات - Database
# sqlite3 مدمج مع Python، لا يحتاج تثبيت منفصل

# إنشاء التقارير PDF - PDF Report Generation
reportlab>=3.6.0

# الرسوم البيانية والمخططات - Charts and Graphs
matplotlib>=3.5.0

# معالجة الصور - Image Processing
Pillow>=9.0.0

# التعامل مع ملفات Excel - Excel File Handling
openpyxl>=3.0.0

# تحليل البيانات - Data Analysis
pandas>=1.4.0
numpy>=1.21.0

# تحويل إلى ملف تنفيذي - Convert to Executable
pyinstaller>=5.0

# مكتبات إضافية مفيدة - Additional Useful Libraries
python-dateutil>=2.8.0
xlsxwriter>=3.0.0
