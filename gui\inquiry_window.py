import tkinter as tk
from tkinter import ttk
import sys
import os

# إضافة المسار الجذر للمشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.helpers import (create_treeview_with_scrollbars, format_date, format_currency, 
                          get_category_name, get_status_name, show_message, center_window)

class InquiryWindow:
    def __init__(self, parent, db_manager, language_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.language_manager = language_manager
        self.search_results = []
        
        # مسح المحتوى السابق
        for widget in self.parent.winfo_children():
            widget.destroy()
        
        self.create_interface()
    
    def create_interface(self):
        """إنشاء واجهة الاستفسار"""
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.parent)
        main_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # العنوان
        title_label = ttk.Label(main_frame, 
                               text=self.language_manager.get_text('inquiry'),
                               font=('Arial', 14, 'bold'))
        title_label.pack(pady=(0, 20))
        
        # إطار البحث
        self.create_search_panel(main_frame)
        
        # إطار النتائج
        self.create_results_panel(main_frame)
        
        # إطار تفاصيل المشترك
        self.create_details_panel(main_frame)
    
    def create_search_panel(self, parent):
        """إنشاء لوحة البحث"""
        search_frame = ttk.LabelFrame(parent, text="البحث والاستفسار", padding="15")
        search_frame.pack(fill='x', pady=(0, 15))
        
        # الصف الأول - البحث المتعدد
        multi_search_frame = ttk.Frame(search_frame)
        multi_search_frame.pack(fill='x', pady=(0, 15))
        
        ttk.Label(multi_search_frame, text="البحث المتعدد:", 
                 font=('Arial', 10, 'bold')).pack(side='left', padx=(0, 10))
        
        self.multi_search_var = tk.StringVar()
        multi_search_entry = ttk.Entry(multi_search_frame, textvariable=self.multi_search_var, 
                                      width=40, font=('Arial', 10))
        multi_search_entry.pack(side='left', padx=(0, 10))
        multi_search_entry.bind('<Return>', lambda e: self.multi_search())
        
        ttk.Button(multi_search_frame, text="🔍 بحث متعدد", 
                  command=self.multi_search).pack(side='left', padx=(0, 5))
        ttk.Button(multi_search_frame, text="🔄 مسح", 
                  command=self.clear_search).pack(side='left')
        
        # فاصل
        ttk.Separator(search_frame, orient='horizontal').pack(fill='x', pady=10)
        
        # الصف الثاني - البحث المحدد
        specific_search_frame = ttk.Frame(search_frame)
        specific_search_frame.pack(fill='x')
        
        # البحث بالاسم
        name_frame = ttk.Frame(specific_search_frame)
        name_frame.pack(side='left', padx=(0, 20))
        
        ttk.Label(name_frame, text="البحث بالاسم:").pack()
        self.name_search_var = tk.StringVar()
        name_entry = ttk.Entry(name_frame, textvariable=self.name_search_var, width=20)
        name_entry.pack(pady=(5, 0))
        name_entry.bind('<Return>', lambda e: self.search_by_name())
        ttk.Button(name_frame, text="بحث", command=self.search_by_name).pack(pady=(5, 0))
        
        # البحث برقم الحساب
        account_frame = ttk.Frame(specific_search_frame)
        account_frame.pack(side='left', padx=(0, 20))
        
        ttk.Label(account_frame, text="البحث برقم الحساب:").pack()
        self.account_search_var = tk.StringVar()
        account_entry = ttk.Entry(account_frame, textvariable=self.account_search_var, width=20)
        account_entry.pack(pady=(5, 0))
        account_entry.bind('<Return>', lambda e: self.search_by_account())
        ttk.Button(account_frame, text="بحث", command=self.search_by_account).pack(pady=(5, 0))
        
        # البحث برقم المقياس
        meter_frame = ttk.Frame(specific_search_frame)
        meter_frame.pack(side='left')
        
        ttk.Label(meter_frame, text="البحث برقم المقياس:").pack()
        self.meter_search_var = tk.StringVar()
        meter_entry = ttk.Entry(meter_frame, textvariable=self.meter_search_var, width=20)
        meter_entry.pack(pady=(5, 0))
        meter_entry.bind('<Return>', lambda e: self.search_by_meter())
        ttk.Button(meter_frame, text="بحث", command=self.search_by_meter).pack(pady=(5, 0))
    
    def create_results_panel(self, parent):
        """إنشاء لوحة النتائج"""
        results_frame = ttk.LabelFrame(parent, text="نتائج البحث", padding="10")
        results_frame.pack(fill='both', expand=True, pady=(0, 15))
        
        # تعريف الأعمدة
        columns = ['ACCTNO', 'NAME_A', 'ADRESS', 'METER_NO', 'OUTS', 'HOUSE_CODE']
        headings = ['رقم الحساب', 'الاسم', 'العنوان', 'رقم المقياس', 'الدين', 'الصنف']
        
        # إنشاء الجدول
        self.results_tree_frame, self.results_tree = create_treeview_with_scrollbars(
            results_frame, columns, headings)
        self.results_tree_frame.pack(fill='both', expand=True)
        
        # تكوين الأعمدة
        self.results_tree.column('ACCTNO', width=120, anchor='center')
        self.results_tree.column('NAME_A', width=200, anchor='center')
        self.results_tree.column('ADRESS', width=150, anchor='center')
        self.results_tree.column('METER_NO', width=120, anchor='center')
        self.results_tree.column('OUTS', width=100, anchor='center')
        self.results_tree.column('HOUSE_CODE', width=100, anchor='center')
        
        # ربط الأحداث
        self.results_tree.bind('<ButtonRelease-1>', self.on_result_select)
        self.results_tree.bind('<Double-1>', self.show_detailed_customer_info)
        
        # شريط معلومات النتائج
        self.results_info_frame = ttk.Frame(results_frame)
        self.results_info_frame.pack(fill='x', pady=(10, 0))
        
        self.results_count_label = ttk.Label(self.results_info_frame, 
                                           text="عدد النتائج: 0", 
                                           font=('Arial', 9, 'bold'))
        self.results_count_label.pack(side='left')
        
        ttk.Button(self.results_info_frame, text="عرض التفاصيل", 
                  command=self.show_detailed_customer_info).pack(side='right')
    
    def create_details_panel(self, parent):
        """إنشاء لوحة التفاصيل"""
        self.details_frame = ttk.LabelFrame(parent, text="تفاصيل المشترك المحدد", padding="10")
        self.details_frame.pack(fill='x')
        
        # رسالة افتراضية
        self.no_selection_label = ttk.Label(self.details_frame, 
                                           text="اختر مشتركاً من نتائج البحث لعرض تفاصيله",
                                           font=('Arial', 10, 'italic'))
        self.no_selection_label.pack(pady=20)
    
    def multi_search(self):
        """البحث المتعدد"""
        search_term = self.multi_search_var.get().strip()
        
        if not search_term:
            show_message("تحذير", "يرجى إدخال كلمة البحث", "warning")
            return
        
        try:
            results = self.db_manager.multi_search(search_term)
            self.display_results(results)
            
        except Exception as e:
            show_message("خطأ", f"فشل في البحث: {str(e)}", "error")
    
    def search_by_name(self):
        """البحث بالاسم"""
        name = self.name_search_var.get().strip()
        
        if not name:
            show_message("تحذير", "يرجى إدخال الاسم", "warning")
            return
        
        try:
            results = self.db_manager.search_by_name(name)
            self.display_results(results)
            
        except Exception as e:
            show_message("خطأ", f"فشل في البحث: {str(e)}", "error")
    
    def search_by_account(self):
        """البحث برقم الحساب"""
        account = self.account_search_var.get().strip()
        
        if not account:
            show_message("تحذير", "يرجى إدخال رقم الحساب", "warning")
            return
        
        try:
            results = self.db_manager.search_by_account(account)
            self.display_results(results)
            
        except Exception as e:
            show_message("خطأ", f"فشل في البحث: {str(e)}", "error")
    
    def search_by_meter(self):
        """البحث برقم المقياس"""
        meter = self.meter_search_var.get().strip()
        
        if not meter:
            show_message("تحذير", "يرجى إدخال رقم المقياس", "warning")
            return
        
        try:
            results = self.db_manager.search_by_meter(meter)
            self.display_results(results)
            
        except Exception as e:
            show_message("خطأ", f"فشل في البحث: {str(e)}", "error")
    
    def display_results(self, results):
        """عرض نتائج البحث"""
        # مسح النتائج السابقة
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)
        
        self.search_results = results
        
        # إضافة النتائج الجديدة
        for row in results:
            values = [
                str(row.get('ACCTNO', '')),
                str(row.get('NAME_A', '')),
                str(row.get('ADRESS', '')),
                str(row.get('METER_NO', '')),
                format_currency(row.get('OUTS', 0)),
                get_category_name(row.get('HOUSE_CODE', 0), self.language_manager.get_current_language())
            ]
            self.results_tree.insert('', 'end', values=values)
        
        # تحديث عدد النتائج
        self.results_count_label.configure(text=f"عدد النتائج: {len(results)}")
        
        # مسح التفاصيل
        self.clear_details()
        
        if not results:
            show_message("معلومات", "لم يتم العثور على نتائج", "info")
    
    def on_result_select(self, event):
        """عند اختيار نتيجة"""
        selection = self.results_tree.selection()
        if selection:
            item = self.results_tree.item(selection[0])
            account_no = item['values'][0]
            self.show_customer_details(account_no)
    
    def show_customer_details(self, account_no):
        """عرض تفاصيل المشترك في اللوحة السفلية"""
        try:
            # البحث عن المشترك
            results = self.db_manager.search_by_account(str(account_no))
            if not results:
                return
            
            customer = results[0]
            
            # مسح التفاصيل السابقة
            self.clear_details()
            
            # إنشاء إطار التفاصيل
            details_content = ttk.Frame(self.details_frame)
            details_content.pack(fill='both', expand=True)
            
            # تقسيم التفاصيل إلى أعمدة
            left_frame = ttk.Frame(details_content)
            left_frame.pack(side='left', fill='both', expand=True, padx=(0, 10))
            
            right_frame = ttk.Frame(details_content)
            right_frame.pack(side='right', fill='both', expand=True)
            
            # العمود الأيسر - البيانات الأساسية
            basic_label = ttk.Label(left_frame, text="البيانات الأساسية", 
                                   font=('Arial', 10, 'bold'))
            basic_label.pack(anchor='w', pady=(0, 10))
            
            basic_data = [
                ("رقم الحساب", customer.get('ACCTNO', '')),
                ("الاسم", customer.get('NAME_A', '')),
                ("العنوان", customer.get('ADRESS', '')),
                ("رقم المنزل", customer.get('HOUSE_NO', '')),
                ("رقم المقياس", customer.get('METER_NO', '')),
                ("الصنف", get_category_name(customer.get('HOUSE_CODE', 0))),
                ("الحالة", get_status_name(customer.get('EVEN_CLOSE', 0)))
            ]
            
            for label, value in basic_data:
                info_frame = ttk.Frame(left_frame)
                info_frame.pack(fill='x', pady=2)
                ttk.Label(info_frame, text=f"{label}:", width=15).pack(side='left')
                ttk.Label(info_frame, text=str(value), font=('Arial', 9, 'bold')).pack(side='left')
            
            # العمود الأيمن - البيانات المالية والقراءات
            financial_label = ttk.Label(right_frame, text="البيانات المالية والقراءات", 
                                       font=('Arial', 10, 'bold'))
            financial_label.pack(anchor='w', pady=(0, 10))
            
            financial_data = [
                ("الدين الحالي", format_currency(customer.get('OUTS', 0))),
                ("الدين السابق", format_currency(customer.get('BKOUTS', 0))),
                ("المدفوع", format_currency(customer.get('PAYMENT', 0))),
                ("القراءة الحالية", customer.get('LAST_READ', 0)),
                ("القراءة السابقة", customer.get('PREV_READ', 0)),
                ("الاستهلاك", customer.get('LAST_READ', 0) - customer.get('PREV_READ', 0)),
                ("تاريخ الفاتورة", format_date(customer.get('BILL_DATE', 0)))
            ]
            
            for label, value in financial_data:
                info_frame = ttk.Frame(right_frame)
                info_frame.pack(fill='x', pady=2)
                ttk.Label(info_frame, text=f"{label}:", width=15).pack(side='left')
                ttk.Label(info_frame, text=str(value), font=('Arial', 9, 'bold')).pack(side='left')
            
        except Exception as e:
            show_message("خطأ", f"فشل في عرض التفاصيل: {str(e)}", "error")
    
    def show_detailed_customer_info(self, event=None):
        """عرض معلومات مفصلة للمشترك في نافذة منفصلة"""
        selection = self.results_tree.selection()
        if not selection:
            show_message("تحذير", "يرجى اختيار مشترك من النتائج", "warning")
            return
        
        account_no = self.results_tree.item(selection[0])['values'][0]
        
        try:
            # البحث عن المشترك
            results = self.db_manager.search_by_account(str(account_no))
            if not results:
                show_message("خطأ", "لم يتم العثور على المشترك", "error")
                return
            
            customer = results[0]
            
            # إنشاء نافذة التفاصيل المفصلة
            self.create_detailed_window(customer)
            
        except Exception as e:
            show_message("خطأ", f"فشل في عرض التفاصيل: {str(e)}", "error")

    def create_detailed_window(self, customer):
        """إنشاء نافذة التفاصيل المفصلة"""
        details_window = tk.Toplevel(self.parent)
        details_window.title(f"تفاصيل شاملة - {customer.get('NAME_A', '')}")
        details_window.geometry("800x600")
        center_window(details_window, 800, 600)

        # إطار رئيسي مع شريط تمرير
        canvas = tk.Canvas(details_window)
        scrollbar = ttk.Scrollbar(details_window, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # المحتوى
        main_content = ttk.Frame(scrollable_frame, padding="20")
        main_content.pack(fill='both', expand=True)

        # العنوان
        title_label = ttk.Label(main_content, text="التفاصيل الشاملة للمشترك",
                               font=('Arial', 16, 'bold'))
        title_label.pack(pady=(0, 20))

        # البيانات الشخصية
        personal_frame = ttk.LabelFrame(main_content, text="البيانات الشخصية", padding="15")
        personal_frame.pack(fill='x', pady=(0, 15))

        personal_data = [
            ("رقم الحساب", customer.get('ACCTNO', '')),
            ("رقم التركيب", customer.get('INSTAL_NO', '')),
            ("الرقم التسلسلي", customer.get('SERIAL', '')),
            ("الاسم", customer.get('NAME_A', '')),
            ("رقم المنزل", customer.get('HOUSE_NO', '')),
            ("العنوان", customer.get('ADRESS', '')),
            ("الصنف", get_category_name(customer.get('HOUSE_CODE', 0))),
            ("الحالة", get_status_name(customer.get('EVEN_CLOSE', 0)))
        ]

        self.create_info_grid(personal_frame, personal_data, 2)

        # بيانات المقياس
        meter_frame = ttk.LabelFrame(main_content, text="بيانات المقياس", padding="15")
        meter_frame.pack(fill='x', pady=(0, 15))

        meter_data = [
            ("رقم المقياس", customer.get('METER_NO', '')),
            ("عدد الأطوار", customer.get('MPHASE', '')),
            ("معامل المقياس", customer.get('METER_FACT', '')),
            ("أجرة المقياس", customer.get('METER_RENT', '')),
            ("أجرة القاطع", customer.get('CB_RENT', '')),
            ("علامة المقياس السيء", "نعم" if customer.get('BAD_M_FLAG', 0) == 1 else "لا")
        ]

        self.create_info_grid(meter_frame, meter_data, 2)

        # بيانات القراءات
        readings_frame = ttk.LabelFrame(main_content, text="بيانات القراءات", padding="15")
        readings_frame.pack(fill='x', pady=(0, 15))

        consumption = customer.get('LAST_READ', 0) - customer.get('PREV_READ', 0)
        readings_data = [
            ("القراءة الحالية", customer.get('LAST_READ', 0)),
            ("تاريخ القراءة الحالية", format_date(customer.get('LAST_DATE', 0))),
            ("القراءة السابقة", customer.get('PREV_READ', 0)),
            ("تاريخ القراءة السابقة", format_date(customer.get('PREV_DATE', 0))),
            ("الاستهلاك", consumption),
            ("متوسط الاستهلاك", customer.get('AV_CONS', 0))
        ]

        self.create_info_grid(readings_frame, readings_data, 2)

        # البيانات المالية
        financial_frame = ttk.LabelFrame(main_content, text="البيانات المالية", padding="15")
        financial_frame.pack(fill='x', pady=(0, 15))

        financial_data = [
            ("الدين الحالي", format_currency(customer.get('OUTS', 0))),
            ("الدين السابق", format_currency(customer.get('BKOUTS', 0))),
            ("الرسوم الأخرى", format_currency(customer.get('OTHCHARGE', 0))),
            ("المدفوع", format_currency(customer.get('PAYMENT', 0))),
            ("تاريخ الدفع", format_date(customer.get('PAY_DATE', 0))),
            ("تاريخ الفاتورة", format_date(customer.get('BILL_DATE', 0)))
        ]

        self.create_info_grid(financial_frame, financial_data, 2)

        # معلومات إضافية
        additional_frame = ttk.LabelFrame(main_content, text="معلومات إضافية", padding="15")
        additional_frame.pack(fill='x', pady=(0, 20))

        additional_data = [
            ("استبدال قديم", customer.get('OLD_EXCH', 0)),
            ("علامة الاحتياط", customer.get('SPERE_FLAG', 0)),
            ("حساب الفاتورة", customer.get('BILL_CAL', 0)),
            ("علامة التشغيل", customer.get('OPR_FLG', 0))
        ]

        self.create_info_grid(additional_frame, additional_data, 2)

        # أزرار التحكم
        buttons_frame = ttk.Frame(main_content)
        buttons_frame.pack(fill='x', pady=10)

        ttk.Button(buttons_frame, text="طباعة",
                  command=lambda: self.print_customer_info(customer)).pack(side='left', padx=(0, 10))
        ttk.Button(buttons_frame, text="تصدير",
                  command=lambda: self.export_customer_info(customer)).pack(side='left', padx=(0, 10))
        ttk.Button(buttons_frame, text="إغلاق",
                  command=details_window.destroy).pack(side='right')

    def create_info_grid(self, parent, data, columns):
        """إنشاء شبكة معلومات"""
        for i, (label, value) in enumerate(data):
            row = i // columns
            col = (i % columns) * 2

            ttk.Label(parent, text=f"{label}:", font=('Arial', 9)).grid(
                row=row, column=col, sticky='w', padx=(0, 10), pady=5)
            ttk.Label(parent, text=str(value), font=('Arial', 9, 'bold')).grid(
                row=row, column=col+1, sticky='w', padx=(0, 30), pady=5)

    def clear_search(self):
        """مسح البحث"""
        self.multi_search_var.set("")
        self.name_search_var.set("")
        self.account_search_var.set("")
        self.meter_search_var.set("")

        # مسح النتائج
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)

        self.results_count_label.configure(text="عدد النتائج: 0")
        self.clear_details()

    def clear_details(self):
        """مسح التفاصيل"""
        for widget in self.details_frame.winfo_children():
            widget.destroy()

        self.no_selection_label = ttk.Label(self.details_frame,
                                           text="اختر مشتركاً من نتائج البحث لعرض تفاصيله",
                                           font=('Arial', 10, 'italic'))
        self.no_selection_label.pack(pady=20)

    def print_customer_info(self, customer):
        """طباعة معلومات المشترك"""
        show_message("معلومات", "سيتم إضافة وظيفة الطباعة قريباً", "info")

    def export_customer_info(self, customer):
        """تصدير معلومات المشترك"""
        try:
            from tkinter import filedialog
            filename = filedialog.asksaveasfilename(
                defaultextension=".txt",
                filetypes=[("Text files", "*.txt"), ("All files", "*.*")],
                title="حفظ معلومات المشترك"
            )

            if filename:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(f"تفاصيل المشترك - {customer.get('NAME_A', '')}\n")
                    f.write("=" * 50 + "\n\n")

                    f.write("البيانات الشخصية:\n")
                    f.write(f"رقم الحساب: {customer.get('ACCTNO', '')}\n")
                    f.write(f"الاسم: {customer.get('NAME_A', '')}\n")
                    f.write(f"العنوان: {customer.get('ADRESS', '')}\n")
                    f.write(f"رقم المقياس: {customer.get('METER_NO', '')}\n")
                    f.write(f"الصنف: {get_category_name(customer.get('HOUSE_CODE', 0))}\n")
                    f.write(f"الحالة: {get_status_name(customer.get('EVEN_CLOSE', 0))}\n\n")

                    f.write("البيانات المالية:\n")
                    f.write(f"الدين الحالي: {format_currency(customer.get('OUTS', 0))}\n")
                    f.write(f"الدين السابق: {format_currency(customer.get('BKOUTS', 0))}\n")
                    f.write(f"المدفوع: {format_currency(customer.get('PAYMENT', 0))}\n\n")

                    f.write("بيانات القراءات:\n")
                    f.write(f"القراءة الحالية: {customer.get('LAST_READ', 0)}\n")
                    f.write(f"القراءة السابقة: {customer.get('PREV_READ', 0)}\n")
                    f.write(f"الاستهلاك: {customer.get('LAST_READ', 0) - customer.get('PREV_READ', 0)}\n")

                show_message("نجح", f"تم حفظ المعلومات في {filename}", "info")

        except Exception as e:
            show_message("خطأ", f"فشل في التصدير: {str(e)}", "error")
