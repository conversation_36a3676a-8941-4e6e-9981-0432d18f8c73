#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام إدارة فواتير الكهرباء
Test Script for Electricity Bills Management System

هذا الملف يحتوي على اختبارات أساسية للتأكد من عمل النظام بشكل صحيح
"""

import sys
import os
import unittest
import sqlite3
from datetime import datetime

# إضافة مسار المشروع
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

class TestElectricityBillsSystem(unittest.TestCase):
    """فئة اختبار النظام"""
    
    def setUp(self):
        """إعداد الاختبارات"""
        self.db_path = "electricity_bills.db"
        
    def test_database_exists(self):
        """اختبار وجود قاعدة البيانات"""
        self.assertTrue(os.path.exists(self.db_path), "ملف قاعدة البيانات غير موجود")
        
    def test_database_connection(self):
        """اختبار الاتصال بقاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            conn.close()
            
            self.assertGreater(len(tables), 0, "قاعدة البيانات فارغة")
            
        except Exception as e:
            self.fail(f"فشل في الاتصال بقاعدة البيانات: {e}")
    
    def test_database_manager_import(self):
        """اختبار استيراد مدير قاعدة البيانات"""
        try:
            from database.db_manager import DatabaseManager
            db_manager = DatabaseManager()
            self.assertIsNotNone(db_manager, "فشل في إنشاء مدير قاعدة البيانات")
        except ImportError as e:
            self.fail(f"فشل في استيراد مدير قاعدة البيانات: {e}")
    
    def test_database_manager_connection(self):
        """اختبار اتصال مدير قاعدة البيانات"""
        try:
            from database.db_manager import DatabaseManager
            db_manager = DatabaseManager()
            result = db_manager.connect()
            self.assertTrue(result, "فشل في اتصال مدير قاعدة البيانات")
            db_manager.disconnect()
        except Exception as e:
            self.fail(f"خطأ في اختبار اتصال مدير قاعدة البيانات: {e}")
    
    def test_language_manager_import(self):
        """اختبار استيراد مدير اللغات"""
        try:
            from utils.language import LanguageManager
            lang_manager = LanguageManager()
            self.assertIsNotNone(lang_manager, "فشل في إنشاء مدير اللغات")
        except ImportError as e:
            self.fail(f"فشل في استيراد مدير اللغات: {e}")
    
    def test_language_manager_functionality(self):
        """اختبار وظائف مدير اللغات"""
        try:
            from utils.language import LanguageManager
            lang_manager = LanguageManager()
            
            # اختبار اللغة الافتراضية
            self.assertEqual(lang_manager.get_current_language(), 'ar', "اللغة الافتراضية ليست العربية")
            
            # اختبار تغيير اللغة
            lang_manager.set_language('en')
            self.assertEqual(lang_manager.get_current_language(), 'en', "فشل في تغيير اللغة")
            
            # اختبار الحصول على النص
            text = lang_manager.get_text('main_title')
            self.assertIsNotNone(text, "فشل في الحصول على النص المترجم")
            
        except Exception as e:
            self.fail(f"خطأ في اختبار مدير اللغات: {e}")
    
    def test_helpers_import(self):
        """اختبار استيراد الدوال المساعدة"""
        try:
            from utils.helpers import format_date, format_currency, get_category_name
            self.assertTrue(callable(format_date), "format_date ليس دالة")
            self.assertTrue(callable(format_currency), "format_currency ليس دالة")
            self.assertTrue(callable(get_category_name), "get_category_name ليس دالة")
        except ImportError as e:
            self.fail(f"فشل في استيراد الدوال المساعدة: {e}")
    
    def test_helpers_functionality(self):
        """اختبار وظائف الدوال المساعدة"""
        try:
            from utils.helpers import format_date, format_currency, get_category_name
            
            # اختبار تنسيق التاريخ
            formatted_date = format_date(20241206)
            self.assertEqual(formatted_date, "06/12/2024", "فشل في تنسيق التاريخ")
            
            # اختبار تنسيق العملة
            formatted_currency = format_currency(1234.56)
            self.assertIn("1,234.56", formatted_currency, "فشل في تنسيق العملة")
            
            # اختبار الحصول على اسم الصنف
            category_name = get_category_name(15, 'ar')
            self.assertEqual(category_name, "منزلي", "فشل في الحصول على اسم الصنف")
            
        except Exception as e:
            self.fail(f"خطأ في اختبار الدوال المساعدة: {e}")
    
    def test_gui_imports(self):
        """اختبار استيراد واجهات المستخدم"""
        gui_modules = [
            'gui.main_window',
            'gui.database_view',
            'gui.inquiry_window',
            'gui.transactions',
            'gui.reports',
            'gui.comparison'
        ]
        
        for module in gui_modules:
            try:
                __import__(module)
            except ImportError as e:
                self.fail(f"فشل في استيراد {module}: {e}")
    
    def test_required_libraries(self):
        """اختبار وجود المكتبات المطلوبة"""
        required_libs = [
            'tkinter',
            'sqlite3',
            'datetime',
            'csv',
            'json'
        ]
        
        for lib in required_libs:
            try:
                __import__(lib)
            except ImportError as e:
                self.fail(f"المكتبة المطلوبة {lib} غير متوفرة: {e}")
    
    def test_optional_libraries(self):
        """اختبار وجود المكتبات الاختيارية"""
        optional_libs = [
            'matplotlib',
            'reportlab',
            'pandas',
            'numpy',
            'PIL',
            'openpyxl'
        ]
        
        missing_libs = []
        for lib in optional_libs:
            try:
                __import__(lib)
            except ImportError:
                missing_libs.append(lib)
        
        if missing_libs:
            print(f"تحذير: المكتبات الاختيارية التالية غير متوفرة: {', '.join(missing_libs)}")
    
    def test_database_data(self):
        """اختبار وجود بيانات في قاعدة البيانات"""
        try:
            from database.db_manager import DatabaseManager
            db_manager = DatabaseManager()
            db_manager.connect()
            
            data = db_manager.get_all_bills(limit=10)
            self.assertGreater(len(data), 0, "لا توجد بيانات في قاعدة البيانات")
            
            # اختبار وجود الحقول المطلوبة
            if data:
                required_fields = ['ACCTNO', 'NAME_A', 'METER_NO', 'OUTS', 'HOUSE_CODE']
                for field in required_fields:
                    self.assertIn(field, data[0], f"الحقل {field} غير موجود في البيانات")
            
            db_manager.disconnect()
            
        except Exception as e:
            self.fail(f"خطأ في اختبار بيانات قاعدة البيانات: {e}")
    
    def test_config_file(self):
        """اختبار ملف التكوين"""
        config_path = "config.json"
        if os.path.exists(config_path):
            try:
                import json
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                # اختبار وجود الأقسام الأساسية
                required_sections = ['application', 'database', 'ui', 'colors']
                for section in required_sections:
                    self.assertIn(section, config, f"القسم {section} غير موجود في ملف التكوين")
                    
            except Exception as e:
                self.fail(f"خطأ في قراءة ملف التكوين: {e}")

def run_tests():
    """تشغيل جميع الاختبارات"""
    print("=" * 60)
    print("اختبار نظام إدارة فواتير الكهرباء")
    print("Testing Electricity Bills Management System")
    print("=" * 60)
    print()
    
    # إنشاء مجموعة الاختبارات
    test_suite = unittest.TestLoader().loadTestsFromTestCase(TestElectricityBillsSystem)
    
    # تشغيل الاختبارات
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    print()
    print("=" * 60)
    print("نتائج الاختبار - Test Results")
    print("=" * 60)
    
    if result.wasSuccessful():
        print("✅ جميع الاختبارات نجحت!")
        print("✅ All tests passed!")
        print()
        print("النظام جاهز للاستخدام")
        print("System is ready to use")
    else:
        print("❌ بعض الاختبارات فشلت")
        print("❌ Some tests failed")
        print()
        print(f"الاختبارات الناجحة: {result.testsRun - len(result.failures) - len(result.errors)}")
        print(f"الاختبارات الفاشلة: {len(result.failures)}")
        print(f"أخطاء: {len(result.errors)}")
        
        if result.failures:
            print("\nالاختبارات الفاشلة:")
            for test, traceback in result.failures:
                print(f"- {test}: {traceback.split('AssertionError: ')[-1].split('\\n')[0]}")
        
        if result.errors:
            print("\nأخطاء:")
            for test, traceback in result.errors:
                print(f"- {test}: {traceback.split('\\n')[-2]}")
    
    print()
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
