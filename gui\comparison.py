import tkinter as tk
from tkinter import ttk
import sys
import os
from datetime import datetime, timedelta

# إضافة المسار الجذر للمشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.helpers import (create_treeview_with_scrollbars, format_date, format_currency, 
                          get_category_name, show_message, get_month_name)

class ComparisonWindow:
    def __init__(self, parent, db_manager, language_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.language_manager = language_manager
        self.comparison_data = []
        
        # مسح المحتوى السابق
        for widget in self.parent.winfo_children():
            widget.destroy()
        
        self.create_interface()
    
    def create_interface(self):
        """إنشاء واجهة المقارنات"""
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.parent)
        main_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # العنوان
        title_label = ttk.Label(main_frame, 
                               text=self.language_manager.get_text('comparison'),
                               font=('Arial', 14, 'bold'))
        title_label.pack(pady=(0, 20))
        
        # إطار اختيار نوع المقارنة
        self.create_comparison_selection(main_frame)
        
        # إطار المعايير
        self.create_criteria_panel(main_frame)
        
        # إطار النتائج
        self.create_results_panel(main_frame)
        
        # إطار الرسوم البيانية
        self.create_charts_panel(main_frame)
    
    def create_comparison_selection(self, parent):
        """إنشاء لوحة اختيار نوع المقارنة"""
        selection_frame = ttk.LabelFrame(parent, text="نوع المقارنة", padding="15")
        selection_frame.pack(fill='x', pady=(0, 15))
        
        # نوع المقارنة
        self.comparison_type = tk.StringVar(value="consumption")
        
        # أزرار اختيار نوع المقارنة
        types_frame = ttk.Frame(selection_frame)
        types_frame.pack(fill='x')
        
        ttk.Radiobutton(types_frame, text="📊 مقارنة الاستهلاكات بين الأشهر", 
                       variable=self.comparison_type, value="consumption",
                       command=self.on_comparison_type_change).pack(side='left', padx=(0, 20))
        
        ttk.Radiobutton(types_frame, text="⚡ مقارنة الضائعات بين القراءات", 
                       variable=self.comparison_type, value="losses",
                       command=self.on_comparison_type_change).pack(side='left', padx=(0, 20))
        
        ttk.Radiobutton(types_frame, text="💰 مقارنة الديون بين الفترات", 
                       variable=self.comparison_type, value="debts",
                       command=self.on_comparison_type_change).pack(side='left', padx=(0, 20))
        
        ttk.Radiobutton(types_frame, text="🏢 مقارنة الأصناف", 
                       variable=self.comparison_type, value="categories",
                       command=self.on_comparison_type_change).pack(side='left')
    
    def create_criteria_panel(self, parent):
        """إنشاء لوحة المعايير"""
        self.criteria_frame = ttk.LabelFrame(parent, text="معايير المقارنة", padding="15")
        self.criteria_frame.pack(fill='x', pady=(0, 15))
        
        # عرض معايير المقارنة الافتراضية
        self.show_criteria_options()
    
    def create_results_panel(self, parent):
        """إنشاء لوحة النتائج"""
        results_frame = ttk.LabelFrame(parent, text="نتائج المقارنة", padding="10")
        results_frame.pack(fill='both', expand=True, pady=(0, 15))
        
        # معلومات المقارنة
        self.results_info_frame = ttk.Frame(results_frame)
        self.results_info_frame.pack(fill='x', pady=(0, 10))
        
        self.results_title_label = ttk.Label(self.results_info_frame, 
                                            text="اختر نوع المقارنة وحدد المعايير",
                                            font=('Arial', 12, 'bold'))
        self.results_title_label.pack(side='left')
        
        self.results_count_label = ttk.Label(self.results_info_frame, 
                                            text="",
                                            font=('Arial', 10))
        self.results_count_label.pack(side='right')
        
        # جدول النتائج
        self.create_results_table(results_frame)
        
        # أزرار التحكم
        buttons_frame = ttk.Frame(results_frame)
        buttons_frame.pack(fill='x', pady=(10, 0))
        
        ttk.Button(buttons_frame, text="📊 إنشاء الرسم البياني", 
                  command=self.generate_chart).pack(side='left', padx=(0, 10))
        ttk.Button(buttons_frame, text="📤 تصدير النتائج", 
                  command=self.export_results).pack(side='left', padx=(0, 10))
        ttk.Button(buttons_frame, text="🔄 تحديث", 
                  command=self.refresh_comparison).pack(side='right')
    
    def create_results_table(self, parent):
        """إنشاء جدول النتائج"""
        # تعريف الأعمدة الافتراضية
        columns = ['period', 'value1', 'value2', 'difference', 'percentage']
        headings = ['الفترة', 'القيمة الأولى', 'القيمة الثانية', 'الفرق', 'النسبة المئوية']
        
        # إنشاء الجدول
        self.results_tree_frame, self.results_tree = create_treeview_with_scrollbars(
            parent, columns, headings)
        self.results_tree_frame.pack(fill='both', expand=True)
        
        # تكوين الأعمدة
        self.results_tree.column('period', width=150, anchor='center')
        self.results_tree.column('value1', width=120, anchor='center')
        self.results_tree.column('value2', width=120, anchor='center')
        self.results_tree.column('difference', width=120, anchor='center')
        self.results_tree.column('percentage', width=100, anchor='center')
    
    def create_charts_panel(self, parent):
        """إنشاء لوحة الرسوم البيانية"""
        self.charts_frame = ttk.LabelFrame(parent, text="الرسوم البيانية", padding="10")
        self.charts_frame.pack(fill='x')
        
        # رسالة افتراضية
        self.no_chart_label = ttk.Label(self.charts_frame, 
                                       text="انقر على 'إنشاء الرسم البياني' لعرض الرسم",
                                       font=('Arial', 10, 'italic'))
        self.no_chart_label.pack(pady=20)
    
    def on_comparison_type_change(self):
        """عند تغيير نوع المقارنة"""
        self.show_criteria_options()
        self.clear_results()
    
    def show_criteria_options(self):
        """عرض خيارات المعايير حسب نوع المقارنة"""
        # مسح المعايير السابقة
        for widget in self.criteria_frame.winfo_children():
            widget.destroy()
        
        comparison_type = self.comparison_type.get()
        
        if comparison_type == "consumption":
            self.show_consumption_criteria()
        elif comparison_type == "losses":
            self.show_losses_criteria()
        elif comparison_type == "debts":
            self.show_debts_criteria()
        elif comparison_type == "categories":
            self.show_categories_criteria()
    
    def show_consumption_criteria(self):
        """عرض معايير مقارنة الاستهلاك"""
        # إطار الفترة الزمنية
        period_frame = ttk.Frame(self.criteria_frame)
        period_frame.pack(fill='x', pady=(0, 15))
        
        ttk.Label(period_frame, text="الفترة الزمنية:", font=('Arial', 10, 'bold')).pack(side='left', padx=(0, 10))
        
        self.consumption_period = tk.StringVar(value="monthly")
        ttk.Radiobutton(period_frame, text="شهرية", variable=self.consumption_period, 
                       value="monthly").pack(side='left', padx=(0, 15))
        ttk.Radiobutton(period_frame, text="ربع سنوية", variable=self.consumption_period, 
                       value="quarterly").pack(side='left', padx=(0, 15))
        ttk.Radiobutton(period_frame, text="سنوية", variable=self.consumption_period, 
                       value="yearly").pack(side='left')
        
        # إطار الفلاتر
        filter_frame = ttk.Frame(self.criteria_frame)
        filter_frame.pack(fill='x', pady=(0, 15))
        
        # فلتر الصنف
        ttk.Label(filter_frame, text="الصنف:").pack(side='left', padx=(0, 5))
        self.consumption_category = tk.StringVar(value="all")
        category_combo = ttk.Combobox(filter_frame, textvariable=self.consumption_category,
                                     values=["الكل", "منزلي", "تجاري", "صناعي", "حكومي"],
                                     state="readonly", width=15)
        category_combo.pack(side='left', padx=(0, 20))
        
        # حد الاستهلاك الأدنى
        ttk.Label(filter_frame, text="الحد الأدنى للاستهلاك:").pack(side='left', padx=(0, 5))
        self.min_consumption = tk.StringVar(value="0")
        ttk.Entry(filter_frame, textvariable=self.min_consumption, width=10).pack(side='left', padx=(0, 20))
        
        # زر التحليل
        ttk.Button(filter_frame, text="🔍 تحليل الاستهلاك", 
                  command=self.analyze_consumption).pack(side='right')
    
    def show_losses_criteria(self):
        """عرض معايير مقارنة الضائعات"""
        # إطار نوع الضائعات
        losses_frame = ttk.Frame(self.criteria_frame)
        losses_frame.pack(fill='x', pady=(0, 15))
        
        ttk.Label(losses_frame, text="نوع الضائعات:", font=('Arial', 10, 'bold')).pack(side='left', padx=(0, 10))
        
        self.losses_type = tk.StringVar(value="technical")
        ttk.Radiobutton(losses_frame, text="ضائعات فنية", variable=self.losses_type, 
                       value="technical").pack(side='left', padx=(0, 15))
        ttk.Radiobutton(losses_frame, text="ضائعات تجارية", variable=self.losses_type, 
                       value="commercial").pack(side='left', padx=(0, 15))
        ttk.Radiobutton(losses_frame, text="إجمالي الضائعات", variable=self.losses_type, 
                       value="total").pack(side='left')
        
        # إطار الفترة
        period_frame = ttk.Frame(self.criteria_frame)
        period_frame.pack(fill='x', pady=(0, 15))
        
        ttk.Label(period_frame, text="فترة المقارنة:").pack(side='left', padx=(0, 5))
        self.losses_period = tk.StringVar(value="6")
        period_combo = ttk.Combobox(period_frame, textvariable=self.losses_period,
                                   values=["3", "6", "12", "24"],
                                   state="readonly", width=10)
        period_combo.pack(side='left', padx=(0, 5))
        ttk.Label(period_frame, text="أشهر").pack(side='left', padx=(0, 20))
        
        # زر التحليل
        ttk.Button(period_frame, text="🔍 تحليل الضائعات", 
                  command=self.analyze_losses).pack(side='right')
    
    def show_debts_criteria(self):
        """عرض معايير مقارنة الديون"""
        # إطار نوع المقارنة
        debt_frame = ttk.Frame(self.criteria_frame)
        debt_frame.pack(fill='x', pady=(0, 15))
        
        ttk.Label(debt_frame, text="نوع مقارنة الديون:", font=('Arial', 10, 'bold')).pack(side='left', padx=(0, 10))
        
        self.debt_comparison_type = tk.StringVar(value="monthly")
        ttk.Radiobutton(debt_frame, text="شهرية", variable=self.debt_comparison_type, 
                       value="monthly").pack(side='left', padx=(0, 15))
        ttk.Radiobutton(debt_frame, text="حسب الصنف", variable=self.debt_comparison_type, 
                       value="category").pack(side='left', padx=(0, 15))
        ttk.Radiobutton(debt_frame, text="حسب المنطقة", variable=self.debt_comparison_type, 
                       value="area").pack(side='left')
        
        # إطار الحد الأدنى للدين
        threshold_frame = ttk.Frame(self.criteria_frame)
        threshold_frame.pack(fill='x', pady=(0, 15))
        
        ttk.Label(threshold_frame, text="الحد الأدنى للدين:").pack(side='left', padx=(0, 5))
        self.min_debt = tk.StringVar(value="1000")
        ttk.Entry(threshold_frame, textvariable=self.min_debt, width=15).pack(side='left', padx=(0, 20))
        
        # زر التحليل
        ttk.Button(threshold_frame, text="🔍 تحليل الديون", 
                  command=self.analyze_debts).pack(side='right')
    
    def show_categories_criteria(self):
        """عرض معايير مقارنة الأصناف"""
        # إطار نوع المقارنة
        category_frame = ttk.Frame(self.criteria_frame)
        category_frame.pack(fill='x', pady=(0, 15))
        
        ttk.Label(category_frame, text="مقارنة الأصناف حسب:", font=('Arial', 10, 'bold')).pack(side='left', padx=(0, 10))
        
        self.category_comparison_type = tk.StringVar(value="consumption")
        ttk.Radiobutton(category_frame, text="الاستهلاك", variable=self.category_comparison_type, 
                       value="consumption").pack(side='left', padx=(0, 15))
        ttk.Radiobutton(category_frame, text="الديون", variable=self.category_comparison_type, 
                       value="debts").pack(side='left', padx=(0, 15))
        ttk.Radiobutton(category_frame, text="عدد الحسابات", variable=self.category_comparison_type, 
                       value="count").pack(side='left')
        
        # زر التحليل
        analysis_frame = ttk.Frame(self.criteria_frame)
        analysis_frame.pack(fill='x', pady=(15, 0))
        
        ttk.Button(analysis_frame, text="🔍 تحليل الأصناف", 
                  command=self.analyze_categories).pack(side='right')
    
    def analyze_consumption(self):
        """تحليل الاستهلاك"""
        try:
            # جلب البيانات
            data = self.db_manager.get_all_bills(limit=5000)
            
            # فلترة البيانات حسب الصنف
            category = self.consumption_category.get()
            if category != "الكل":
                category_codes = {"منزلي": 15, "تجاري": 96, "صناعي": 97, "حكومي": 98}
                if category in category_codes:
                    data = [row for row in data if row.get('HOUSE_CODE') == category_codes[category]]
            
            # فلترة حسب الحد الأدنى
            min_cons = int(self.min_consumption.get() or 0)
            
            # تحليل البيانات
            consumption_analysis = []
            for row in data:
                consumption = row.get('LAST_READ', 0) - row.get('PREV_READ', 0)
                if consumption >= min_cons:
                    consumption_analysis.append({
                        'account': row.get('ACCTNO'),
                        'name': row.get('NAME_A'),
                        'consumption': consumption,
                        'category': get_category_name(row.get('HOUSE_CODE', 0))
                    })
            
            # ترتيب حسب الاستهلاك
            consumption_analysis.sort(key=lambda x: x['consumption'], reverse=True)
            
            # عرض النتائج
            self.display_consumption_results(consumption_analysis)
            
        except Exception as e:
            show_message("خطأ", f"فشل في تحليل الاستهلاك: {str(e)}", "error")

    def analyze_losses(self):
        """تحليل الضائعات"""
        try:
            # جلب البيانات
            data = self.db_manager.get_all_bills(limit=5000)

            # حساب الضائعات (مثال بسيط)
            losses_analysis = []
            total_consumption = 0
            total_billed = 0

            for row in data:
                consumption = row.get('LAST_READ', 0) - row.get('PREV_READ', 0)
                if consumption > 0:
                    total_consumption += consumption
                    # افتراض أن الفاتورة تمثل الاستهلاك المحاسب عليه
                    billed_consumption = consumption * 0.95  # افتراض 5% ضائعات
                    total_billed += billed_consumption

            # حساب نسبة الضائعات
            if total_consumption > 0:
                loss_percentage = ((total_consumption - total_billed) / total_consumption) * 100

                losses_analysis.append({
                    'period': 'الفترة الحالية',
                    'total_consumption': total_consumption,
                    'billed_consumption': total_billed,
                    'losses': total_consumption - total_billed,
                    'loss_percentage': loss_percentage
                })

            # عرض النتائج
            self.display_losses_results(losses_analysis)

        except Exception as e:
            show_message("خطأ", f"فشل في تحليل الضائعات: {str(e)}", "error")

    def analyze_debts(self):
        """تحليل الديون"""
        try:
            # جلب البيانات
            data = self.db_manager.get_debt_report()

            min_debt = float(self.min_debt.get() or 0)
            comparison_type = self.debt_comparison_type.get()

            debt_analysis = []

            if comparison_type == "category":
                # تحليل حسب الصنف
                categories = {}
                for row in data:
                    if row.get('OUTS', 0) >= min_debt:
                        category = row.get('HOUSE_CODE', 0)
                        category_name = get_category_name(category)

                        if category_name not in categories:
                            categories[category_name] = {'count': 0, 'total_debt': 0}

                        categories[category_name]['count'] += 1
                        categories[category_name]['total_debt'] += row.get('OUTS', 0)

                for category, info in categories.items():
                    debt_analysis.append({
                        'category': category,
                        'count': info['count'],
                        'total_debt': info['total_debt'],
                        'average_debt': info['total_debt'] / info['count'] if info['count'] > 0 else 0
                    })

            else:
                # تحليل شهري بسيط
                total_debt = sum(row.get('OUTS', 0) for row in data if row.get('OUTS', 0) >= min_debt)
                count = len([row for row in data if row.get('OUTS', 0) >= min_debt])

                debt_analysis.append({
                    'period': 'الفترة الحالية',
                    'count': count,
                    'total_debt': total_debt,
                    'average_debt': total_debt / count if count > 0 else 0
                })

            # عرض النتائج
            self.display_debt_results(debt_analysis)

        except Exception as e:
            show_message("خطأ", f"فشل في تحليل الديون: {str(e)}", "error")

    def analyze_categories(self):
        """تحليل الأصناف"""
        try:
            # جلب البيانات
            data = self.db_manager.get_all_bills(limit=5000)

            comparison_type = self.category_comparison_type.get()
            categories_analysis = {}

            for row in data:
                category = row.get('HOUSE_CODE', 0)
                category_name = get_category_name(category)

                if category_name not in categories_analysis:
                    categories_analysis[category_name] = {
                        'count': 0,
                        'total_consumption': 0,
                        'total_debt': 0
                    }

                categories_analysis[category_name]['count'] += 1

                if comparison_type == "consumption":
                    consumption = row.get('LAST_READ', 0) - row.get('PREV_READ', 0)
                    categories_analysis[category_name]['total_consumption'] += consumption
                elif comparison_type == "debts":
                    categories_analysis[category_name]['total_debt'] += row.get('OUTS', 0)

            # تحويل إلى قائمة للعرض
            analysis_results = []
            for category, info in categories_analysis.items():
                result = {
                    'category': category,
                    'count': info['count']
                }

                if comparison_type == "consumption":
                    result['total_consumption'] = info['total_consumption']
                    result['average_consumption'] = info['total_consumption'] / info['count'] if info['count'] > 0 else 0
                elif comparison_type == "debts":
                    result['total_debt'] = info['total_debt']
                    result['average_debt'] = info['total_debt'] / info['count'] if info['count'] > 0 else 0

                analysis_results.append(result)

            # عرض النتائج
            self.display_category_results(analysis_results, comparison_type)

        except Exception as e:
            show_message("خطأ", f"فشل في تحليل الأصناف: {str(e)}", "error")

    def display_consumption_results(self, data):
        """عرض نتائج تحليل الاستهلاك"""
        self.comparison_data = data
        self.results_title_label.configure(text="نتائج تحليل الاستهلاك")
        self.results_count_label.configure(text=f"عدد الحسابات: {len(data)}")

        # تحديث عناوين الأعمدة
        self.results_tree.heading('period', text='رقم الحساب')
        self.results_tree.heading('value1', text='اسم المشترك')
        self.results_tree.heading('value2', text='الاستهلاك')
        self.results_tree.heading('difference', text='الصنف')
        self.results_tree.heading('percentage', text='الترتيب')

        # مسح البيانات السابقة
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)

        # عرض البيانات
        for i, row in enumerate(data[:50]):  # عرض أول 50 نتيجة
            values = [
                str(row['account']),
                str(row['name'])[:20],
                f"{row['consumption']:,}",
                row['category'],
                str(i + 1)
            ]
            self.results_tree.insert('', 'end', values=values)

    def display_losses_results(self, data):
        """عرض نتائج تحليل الضائعات"""
        self.comparison_data = data
        self.results_title_label.configure(text="نتائج تحليل الضائعات")
        self.results_count_label.configure(text=f"عدد الفترات: {len(data)}")

        # تحديث عناوين الأعمدة
        self.results_tree.heading('period', text='الفترة')
        self.results_tree.heading('value1', text='إجمالي الاستهلاك')
        self.results_tree.heading('value2', text='الاستهلاك المحاسب')
        self.results_tree.heading('difference', text='الضائعات')
        self.results_tree.heading('percentage', text='نسبة الضائعات')

        # مسح البيانات السابقة
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)

        # عرض البيانات
        for row in data:
            values = [
                row['period'],
                f"{row['total_consumption']:,.0f}",
                f"{row['billed_consumption']:,.0f}",
                f"{row['losses']:,.0f}",
                f"{row['loss_percentage']:.2f}%"
            ]
            self.results_tree.insert('', 'end', values=values)

    def display_debt_results(self, data):
        """عرض نتائج تحليل الديون"""
        self.comparison_data = data
        self.results_title_label.configure(text="نتائج تحليل الديون")
        self.results_count_label.configure(text=f"عدد الفئات: {len(data)}")

        # تحديث عناوين الأعمدة
        if 'category' in data[0]:
            self.results_tree.heading('period', text='الصنف')
        else:
            self.results_tree.heading('period', text='الفترة')

        self.results_tree.heading('value1', text='عدد الحسابات')
        self.results_tree.heading('value2', text='إجمالي الديون')
        self.results_tree.heading('difference', text='متوسط الدين')
        self.results_tree.heading('percentage', text='النسبة')

        # مسح البيانات السابقة
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)

        # حساب إجمالي الديون للنسب المئوية
        total_debt = sum(row['total_debt'] for row in data)

        # عرض البيانات
        for row in data:
            percentage = (row['total_debt'] / total_debt * 100) if total_debt > 0 else 0

            values = [
                row.get('category', row.get('period', '')),
                str(row['count']),
                format_currency(row['total_debt']),
                format_currency(row['average_debt']),
                f"{percentage:.1f}%"
            ]
            self.results_tree.insert('', 'end', values=values)

    def display_category_results(self, data, comparison_type):
        """عرض نتائج تحليل الأصناف"""
        self.comparison_data = data
        self.results_title_label.configure(text=f"نتائج تحليل الأصناف - {comparison_type}")
        self.results_count_label.configure(text=f"عدد الأصناف: {len(data)}")

        # تحديث عناوين الأعمدة
        self.results_tree.heading('period', text='الصنف')
        self.results_tree.heading('value1', text='عدد الحسابات')

        if comparison_type == "consumption":
            self.results_tree.heading('value2', text='إجمالي الاستهلاك')
            self.results_tree.heading('difference', text='متوسط الاستهلاك')
        elif comparison_type == "debts":
            self.results_tree.heading('value2', text='إجمالي الديون')
            self.results_tree.heading('difference', text='متوسط الدين')
        else:
            self.results_tree.heading('value2', text='النسبة')
            self.results_tree.heading('difference', text='الترتيب')

        self.results_tree.heading('percentage', text='النسبة المئوية')

        # مسح البيانات السابقة
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)

        # حساب الإجماليات للنسب المئوية
        if comparison_type == "consumption":
            total_value = sum(row['total_consumption'] for row in data)
        elif comparison_type == "debts":
            total_value = sum(row['total_debt'] for row in data)
        else:
            total_value = sum(row['count'] for row in data)

        # عرض البيانات
        for row in data:
            if comparison_type == "consumption":
                value2 = f"{row['total_consumption']:,}"
                difference = f"{row['average_consumption']:,.0f}"
                percentage = (row['total_consumption'] / total_value * 100) if total_value > 0 else 0
            elif comparison_type == "debts":
                value2 = format_currency(row['total_debt'])
                difference = format_currency(row['average_debt'])
                percentage = (row['total_debt'] / total_value * 100) if total_value > 0 else 0
            else:
                value2 = f"{(row['count'] / total_value * 100):.1f}%" if total_value > 0 else "0%"
                difference = "-"
                percentage = (row['count'] / total_value * 100) if total_value > 0 else 0

            values = [
                row['category'],
                str(row['count']),
                value2,
                difference,
                f"{percentage:.1f}%"
            ]
            self.results_tree.insert('', 'end', values=values)

    def generate_chart(self):
        """إنشاء الرسم البياني"""
        if not self.comparison_data:
            show_message("تحذير", "لا توجد بيانات لإنشاء الرسم البياني", "warning")
            return

        try:
            import matplotlib.pyplot as plt
            import matplotlib
            matplotlib.use('TkAgg')
            from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg

            # مسح الرسم السابق
            for widget in self.charts_frame.winfo_children():
                widget.destroy()

            # إنشاء الرسم
            fig, ax = plt.subplots(figsize=(10, 6))

            comparison_type = self.comparison_type.get()

            if comparison_type == "consumption":
                # رسم بياني للاستهلاك
                names = [row['name'][:10] for row in self.comparison_data[:10]]
                consumptions = [row['consumption'] for row in self.comparison_data[:10]]

                ax.bar(names, consumptions)
                ax.set_title('أعلى 10 مشتركين في الاستهلاك')
                ax.set_ylabel('الاستهلاك (كيلو واط ساعة)')
                plt.xticks(rotation=45)

            elif comparison_type == "categories":
                # رسم دائري للأصناف
                categories = [row['category'] for row in self.comparison_data]
                counts = [row['count'] for row in self.comparison_data]

                ax.pie(counts, labels=categories, autopct='%1.1f%%')
                ax.set_title('توزيع الحسابات حسب الصنف')

            # عرض الرسم في الواجهة
            canvas = FigureCanvasTkAgg(fig, self.charts_frame)
            canvas.draw()
            canvas.get_tk_widget().pack(fill='both', expand=True)

        except ImportError:
            show_message("خطأ", "مكتبة matplotlib غير مثبتة. يرجى تثبيتها لعرض الرسوم البيانية", "error")
        except Exception as e:
            show_message("خطأ", f"فشل في إنشاء الرسم البياني: {str(e)}", "error")

    def export_results(self):
        """تصدير النتائج"""
        if not self.comparison_data:
            show_message("تحذير", "لا توجد نتائج للتصدير", "warning")
            return

        try:
            from tkinter import filedialog
            filename = filedialog.asksaveasfilename(
                defaultextension=".csv",
                filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
                title="حفظ نتائج المقارنة"
            )

            if filename:
                import csv
                with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    if self.comparison_data:
                        fieldnames = list(self.comparison_data[0].keys())
                        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                        writer.writeheader()
                        writer.writerows(self.comparison_data)

                show_message("نجح", f"تم تصدير النتائج إلى {filename}", "info")

        except Exception as e:
            show_message("خطأ", f"فشل في التصدير: {str(e)}", "error")

    def refresh_comparison(self):
        """تحديث المقارنة"""
        comparison_type = self.comparison_type.get()

        if comparison_type == "consumption":
            self.analyze_consumption()
        elif comparison_type == "losses":
            self.analyze_losses()
        elif comparison_type == "debts":
            self.analyze_debts()
        elif comparison_type == "categories":
            self.analyze_categories()

    def clear_results(self):
        """مسح النتائج"""
        self.comparison_data = []
        self.results_title_label.configure(text="اختر نوع المقارنة وحدد المعايير")
        self.results_count_label.configure(text="")

        # مسح الجدول
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)

        # مسح الرسم البياني
        for widget in self.charts_frame.winfo_children():
            widget.destroy()

        self.no_chart_label = ttk.Label(self.charts_frame,
                                       text="انقر على 'إنشاء الرسم البياني' لعرض الرسم",
                                       font=('Arial', 10, 'italic'))
        self.no_chart_label.pack(pady=20)
