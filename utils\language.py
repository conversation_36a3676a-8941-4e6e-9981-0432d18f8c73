class LanguageManager:
    def __init__(self):
        self.current_language = 'ar'  # افتراضي العربية
        self.translations = {
            'ar': {
                # النافذة الرئيسية
                'main_title': 'نظام إدارة فواتير الكهرباء',
                'database_view': 'عرض قاعدة البيانات',
                'inquiry': 'الاستفسارات',
                'transactions': 'المعاملات',
                'reports': 'التقارير',
                'comparison': 'المقارنات',
                'settings': 'الإعدادات',
                'exit': 'خروج',
                
                # عرض قاعدة البيانات
                'account_no': 'رقم الحساب',
                'name': 'الاسم',
                'address': 'العنوان',
                'meter_no': 'رقم المقياس',
                'debt': 'الدين',
                'category': 'الصنف',
                'status': 'الحالة',
                'search': 'بحث',
                'filter': 'فلترة',
                'refresh': 'تحديث',
                'export': 'تصدير',
                
                # الاستفسارات
                'search_term': 'كلمة البحث',
                'search_by_name': 'البحث بالاسم',
                'search_by_account': 'البحث برقم الحساب',
                'search_by_meter': 'البحث برقم المقياس',
                'multi_search': 'البحث المتعدد',
                'customer_details': 'تفاصيل المشترك',
                
                # المعاملات
                'transaction_type': 'نوع المعاملة',
                'meter_replacement': 'استبدال مقياس',
                'account_closure': 'إغلاق حساب',
                'name_change': 'تغيير الاسم',
                'category_change': 'تغيير الصنف',
                'new_meter_no': 'رقم المقياس الجديد',
                'new_name': 'الاسم الجديد',
                'new_category': 'الصنف الجديد',
                'save': 'حفظ',
                'delete': 'حذف',
                'edit': 'تعديل',
                'add': 'إضافة',
                
                # التقارير
                'high_consumption': 'الاستهلاك العالي',
                'debt_report': 'تقرير الديون',
                'commercial_accounts': 'الحسابات التجارية',
                'residential_accounts': 'الحسابات المنزلية',
                'new_accounts': 'الحسابات الجديدة',
                'print_report': 'طباعة التقرير',
                'generate_report': 'إنشاء التقرير',
                
                # المقارنات
                'consumption_comparison': 'مقارنة الاستهلاكات',
                'monthly_losses': 'الضائعات الشهرية',
                'between_months': 'بين الأشهر',
                'generate_chart': 'إنشاء الرسم البياني',
                
                # عام
                'open': 'مفتوح',
                'closed': 'مغلق',
                'residential': 'منزلي',
                'commercial': 'تجاري',
                'industrial': 'صناعي',
                'government': 'حكومي',
                'total': 'المجموع',
                'count': 'العدد',
                'average': 'المتوسط',
                'maximum': 'الأقصى',
                'minimum': 'الأدنى',
                'date': 'التاريخ',
                'amount': 'المبلغ',
                'consumption': 'الاستهلاك',
                'previous_reading': 'القراءة السابقة',
                'current_reading': 'القراءة الحالية',
                'bill_date': 'تاريخ الفاتورة',
                'payment_date': 'تاريخ الدفع',
                'success': 'نجح',
                'error': 'خطأ',
                'warning': 'تحذير',
                'info': 'معلومات',
                'confirm': 'تأكيد',
                'cancel': 'إلغاء',
                'yes': 'نعم',
                'no': 'لا',
                'ok': 'موافق',
                'close': 'إغلاق',
                'language': 'اللغة',
                'arabic': 'العربية',
                'english': 'الإنجليزية'
            },
            'en': {
                # Main Window
                'main_title': 'Electricity Bills Management System',
                'database_view': 'Database View',
                'inquiry': 'Inquiry',
                'transactions': 'Transactions',
                'reports': 'Reports',
                'comparison': 'Comparison',
                'settings': 'Settings',
                'exit': 'Exit',
                
                # Database View
                'account_no': 'Account No',
                'name': 'Name',
                'address': 'Address',
                'meter_no': 'Meter No',
                'debt': 'Debt',
                'category': 'Category',
                'status': 'Status',
                'search': 'Search',
                'filter': 'Filter',
                'refresh': 'Refresh',
                'export': 'Export',
                
                # Inquiry
                'search_term': 'Search Term',
                'search_by_name': 'Search by Name',
                'search_by_account': 'Search by Account',
                'search_by_meter': 'Search by Meter',
                'multi_search': 'Multi Search',
                'customer_details': 'Customer Details',
                
                # Transactions
                'transaction_type': 'Transaction Type',
                'meter_replacement': 'Meter Replacement',
                'account_closure': 'Account Closure',
                'name_change': 'Name Change',
                'category_change': 'Category Change',
                'new_meter_no': 'New Meter No',
                'new_name': 'New Name',
                'new_category': 'New Category',
                'save': 'Save',
                'delete': 'Delete',
                'edit': 'Edit',
                'add': 'Add',
                
                # Reports
                'high_consumption': 'High Consumption',
                'debt_report': 'Debt Report',
                'commercial_accounts': 'Commercial Accounts',
                'residential_accounts': 'Residential Accounts',
                'new_accounts': 'New Accounts',
                'print_report': 'Print Report',
                'generate_report': 'Generate Report',
                
                # Comparison
                'consumption_comparison': 'Consumption Comparison',
                'monthly_losses': 'Monthly Losses',
                'between_months': 'Between Months',
                'generate_chart': 'Generate Chart',
                
                # General
                'open': 'Open',
                'closed': 'Closed',
                'residential': 'Residential',
                'commercial': 'Commercial',
                'industrial': 'Industrial',
                'government': 'Government',
                'total': 'Total',
                'count': 'Count',
                'average': 'Average',
                'maximum': 'Maximum',
                'minimum': 'Minimum',
                'date': 'Date',
                'amount': 'Amount',
                'consumption': 'Consumption',
                'previous_reading': 'Previous Reading',
                'current_reading': 'Current Reading',
                'bill_date': 'Bill Date',
                'payment_date': 'Payment Date',
                'success': 'Success',
                'error': 'Error',
                'warning': 'Warning',
                'info': 'Information',
                'confirm': 'Confirm',
                'cancel': 'Cancel',
                'yes': 'Yes',
                'no': 'No',
                'ok': 'OK',
                'close': 'Close',
                'language': 'Language',
                'arabic': 'Arabic',
                'english': 'English'
            }
        }
    
    def set_language(self, language: str):
        """تغيير اللغة"""
        if language in self.translations:
            self.current_language = language
    
    def get_text(self, key: str) -> str:
        """الحصول على النص المترجم"""
        return self.translations[self.current_language].get(key, key)
    
    def get_current_language(self) -> str:
        """الحصول على اللغة الحالية"""
        return self.current_language
    
    def is_rtl(self) -> bool:
        """التحقق من اتجاه الكتابة"""
        return self.current_language == 'ar'
