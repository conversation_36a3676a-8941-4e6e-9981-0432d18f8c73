import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime
import re
from typing import Any, Dict, List

def format_date(date_int: int) -> str:
    """تحويل التاريخ من صيغة YYYYMMDD إلى نص"""
    if not date_int or date_int == 0:
        return ""
    
    try:
        date_str = str(date_int)
        if len(date_str) == 8:
            year = date_str[:4]
            month = date_str[4:6]
            day = date_str[6:8]
            return f"{day}/{month}/{year}"
        return str(date_int)
    except:
        return str(date_int)

def format_currency(amount: float) -> str:
    """تنسيق المبلغ كعملة"""
    if amount is None:
        return "0.00"
    return f"{amount:,.2f}"

def format_number(number: Any) -> str:
    """تنسيق الأرقام"""
    if number is None:
        return "0"
    try:
        return f"{float(number):,.0f}"
    except:
        return str(number)

def get_category_name(house_code: int, language: str = 'ar') -> str:
    """الحصول على اسم الصنف"""
    categories = {
        'ar': {
            15: 'منزلي',
            96: 'تجاري',
            97: 'صناعي',
            98: 'حكومي'
        },
        'en': {
            15: 'Residential',
            96: 'Commercial',
            97: 'Industrial',
            98: 'Government'
        }
    }
    
    return categories.get(language, {}).get(house_code, f"غير معروف ({house_code})")

def get_status_name(status: int, language: str = 'ar') -> str:
    """الحصول على اسم الحالة"""
    statuses = {
        'ar': {0: 'مفتوح', 1: 'مغلق'},
        'en': {0: 'Open', 1: 'Closed'}
    }
    
    return statuses.get(language, {}).get(status, str(status))

def validate_account_number(account_no: str) -> bool:
    """التحقق من صحة رقم الحساب"""
    if not account_no:
        return False
    
    # التحقق من أن الرقم يحتوي على أرقام فقط
    return account_no.isdigit() and len(account_no) >= 8

def validate_meter_number(meter_no: str) -> bool:
    """التحقق من صحة رقم المقياس"""
    if not meter_no:
        return False
    
    try:
        float(meter_no)
        return True
    except:
        return False

def validate_name(name: str) -> bool:
    """التحقق من صحة الاسم"""
    if not name or len(name.strip()) < 2:
        return False
    return True

def create_treeview_with_scrollbars(parent, columns: List[str], headings: List[str]) -> tuple:
    """إنشاء Treeview مع أشرطة التمرير"""
    # إطار للجدول
    tree_frame = ttk.Frame(parent)
    
    # إنشاء الجدول
    tree = ttk.Treeview(tree_frame, columns=columns, show='headings', height=15)
    
    # تعيين العناوين
    for col, heading in zip(columns, headings):
        tree.heading(col, text=heading)
        tree.column(col, width=120, anchor='center')
    
    # أشرطة التمرير
    v_scrollbar = ttk.Scrollbar(tree_frame, orient="vertical", command=tree.yview)
    h_scrollbar = ttk.Scrollbar(tree_frame, orient="horizontal", command=tree.xview)
    
    tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
    
    # ترتيب العناصر
    tree.grid(row=0, column=0, sticky='nsew')
    v_scrollbar.grid(row=0, column=1, sticky='ns')
    h_scrollbar.grid(row=1, column=0, sticky='ew')
    
    # تكوين الشبكة
    tree_frame.grid_rowconfigure(0, weight=1)
    tree_frame.grid_columnconfigure(0, weight=1)
    
    return tree_frame, tree

def show_message(title: str, message: str, msg_type: str = 'info'):
    """عرض رسالة للمستخدم"""
    if msg_type == 'error':
        messagebox.showerror(title, message)
    elif msg_type == 'warning':
        messagebox.showwarning(title, message)
    elif msg_type == 'question':
        return messagebox.askyesno(title, message)
    else:
        messagebox.showinfo(title, message)

def center_window(window, width: int, height: int):
    """توسيط النافذة على الشاشة"""
    screen_width = window.winfo_screenwidth()
    screen_height = window.winfo_screenheight()
    
    x = (screen_width - width) // 2
    y = (screen_height - height) // 2
    
    window.geometry(f"{width}x{height}+{x}+{y}")

def configure_rtl_layout(widget, is_rtl: bool = True):
    """تكوين التخطيط للغة العربية"""
    if is_rtl:
        try:
            widget.configure(justify='right')
        except:
            pass

def export_to_csv(data: List[Dict], filename: str, headers: List[str]):
    """تصدير البيانات إلى ملف CSV"""
    import csv
    
    try:
        with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
            if not data:
                return False
            
            writer = csv.DictWriter(csvfile, fieldnames=headers)
            writer.writeheader()
            
            for row in data:
                # تنسيق البيانات
                formatted_row = {}
                for header in headers:
                    value = row.get(header, '')
                    if isinstance(value, float):
                        formatted_row[header] = format_currency(value)
                    elif header in ['LAST_DATE', 'PREV_DATE', 'PAY_DATE', 'BILL_DATE']:
                        formatted_row[header] = format_date(value)
                    else:
                        formatted_row[header] = str(value)
                
                writer.writerow(formatted_row)
        
        return True
    except Exception as e:
        print(f"خطأ في التصدير: {e}")
        return False

def calculate_consumption(last_read: int, prev_read: int, meter_fact: int = 1) -> int:
    """حساب الاستهلاك"""
    if not last_read or not prev_read:
        return 0
    
    consumption = (last_read - prev_read) * meter_fact
    return max(0, consumption)  # تجنب القيم السالبة

def get_month_name(month: int, language: str = 'ar') -> str:
    """الحصول على اسم الشهر"""
    months = {
        'ar': {
            1: 'يناير', 2: 'فبراير', 3: 'مارس', 4: 'أبريل',
            5: 'مايو', 6: 'يونيو', 7: 'يوليو', 8: 'أغسطس',
            9: 'سبتمبر', 10: 'أكتوبر', 11: 'نوفمبر', 12: 'ديسمبر'
        },
        'en': {
            1: 'January', 2: 'February', 3: 'March', 4: 'April',
            5: 'May', 6: 'June', 7: 'July', 8: 'August',
            9: 'September', 10: 'October', 11: 'November', 12: 'December'
        }
    }
    
    return months.get(language, {}).get(month, str(month))

def parse_date_from_int(date_int: int) -> datetime:
    """تحويل التاريخ من صيغة YYYYMMDD إلى datetime"""
    try:
        date_str = str(date_int)
        if len(date_str) == 8:
            year = int(date_str[:4])
            month = int(date_str[4:6])
            day = int(date_str[6:8])
            return datetime(year, month, day)
    except:
        pass
    
    return datetime.now()

def create_styled_button(parent, text: str, command, style: str = 'primary') -> ttk.Button:
    """إنشاء زر منسق"""
    button = ttk.Button(parent, text=text, command=command)
    
    # يمكن إضافة تنسيقات مخصصة هنا
    if style == 'primary':
        button.configure(width=15)
    elif style == 'secondary':
        button.configure(width=12)
    elif style == 'danger':
        button.configure(width=10)
    
    return button
