@echo off
chcp 65001 >nul
title إنشاء ملف تنفيذي - Build Executable

echo ================================================
echo    إنشاء ملف تنفيذي
echo    Building Executable File
echo ================================================
echo.

echo فحص المتطلبات...
echo Checking requirements...
echo.

REM فحص وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: Python غير مثبت
    echo Error: Python is not installed
    pause
    exit /b 1
)

echo Python ✓
echo.

REM فحص وجود PyInstaller
python -c "import PyInstaller" >nul 2>&1
if errorlevel 1 (
    echo خطأ: PyInstaller غير مثبت
    echo Error: PyInstaller is not installed
    echo.
    echo تثبيت PyInstaller...
    echo Installing PyInstaller...
    pip install pyinstaller
    echo.
)

echo PyInstaller ✓
echo.

REM فحص وجود الملفات المطلوبة
if not exist "main.py" (
    echo خطأ: main.py غير موجود
    echo Error: main.py not found
    pause
    exit /b 1
)

if not exist "electricity_bills.db" (
    echo خطأ: electricity_bills.db غير موجود
    echo Error: electricity_bills.db not found
    pause
    exit /b 1
)

echo الملفات المطلوبة موجودة ✓
echo Required files found ✓
echo.

echo ================================================
echo بدء إنشاء الملف التنفيذي...
echo Starting executable creation...
echo ================================================
echo.

REM إنشاء مجلد للبناء إذا لم يكن موجوداً
if not exist "build" mkdir build
if not exist "dist" mkdir dist

echo تنظيف الملفات السابقة...
echo Cleaning previous files...
if exist "dist\*.exe" del /q "dist\*.exe"
if exist "build\*" rmdir /s /q "build"
if exist "*.spec" del /q "*.spec"
echo.

echo إنشاء الملف التنفيذي...
echo Creating executable...
echo.

REM إنشاء الملف التنفيذي مع جميع التبعيات
pyinstaller ^
    --onefile ^
    --windowed ^
    --name="ElectricityBillsManager" ^
    --add-data="electricity_bills.db;." ^
    --add-data="database;database" ^
    --add-data="gui;gui" ^
    --add-data="utils;utils" ^
    --hidden-import="tkinter" ^
    --hidden-import="sqlite3" ^
    --hidden-import="matplotlib" ^
    --hidden-import="reportlab" ^
    --hidden-import="pandas" ^
    --hidden-import="numpy" ^
    --hidden-import="PIL" ^
    --hidden-import="openpyxl" ^
    --collect-all="matplotlib" ^
    --collect-all="reportlab" ^
    main.py

echo.

REM فحص نجاح العملية
if exist "dist\ElectricityBillsManager.exe" (
    echo ================================================
    echo تم إنشاء الملف التنفيذي بنجاح! ✓
    echo Executable created successfully! ✓
    echo ================================================
    echo.
    
    echo مكان الملف:
    echo File location:
    echo %cd%\dist\ElectricityBillsManager.exe
    echo.
    
    echo حجم الملف:
    echo File size:
    for %%A in ("dist\ElectricityBillsManager.exe") do echo %%~zA bytes
    echo.
    
    echo يمكنك الآن نسخ الملف التنفيذي إلى أي مكان وتشغيله
    echo You can now copy the executable anywhere and run it
    echo.
    
    REM سؤال المستخدم إذا كان يريد تشغيل الملف
    set /p choice="هل تريد تشغيل الملف التنفيذي الآن؟ (y/n): "
    if /i "%choice%"=="y" (
        echo.
        echo تشغيل الملف التنفيذي...
        echo Running executable...
        start "" "dist\ElectricityBillsManager.exe"
    )
    
) else (
    echo ================================================
    echo فشل في إنشاء الملف التنفيذي ✗
    echo Failed to create executable ✗
    echo ================================================
    echo.
    
    echo يرجى مراجعة الأخطاء أعلاه
    echo Please check the errors above
    echo.
    
    echo نصائح لحل المشاكل:
    echo Troubleshooting tips:
    echo.
    echo 1. تأكد من تثبيت جميع المكتبات المطلوبة
    echo    Make sure all required libraries are installed
    echo.
    echo 2. تأكد من وجود مساحة كافية على القرص الصلب
    echo    Ensure sufficient disk space
    echo.
    echo 3. أغلق برامج مكافحة الفيروسات مؤقتاً
    echo    Temporarily disable antivirus software
    echo.
    echo 4. شغل الأمر كمدير
    echo    Run as administrator
    echo.
)

echo.
echo ملاحظات مهمة:
echo Important notes:
echo.
echo - الملف التنفيذي يحتوي على جميع التبعيات
echo   The executable contains all dependencies
echo.
echo - حجم الملف قد يكون كبير (50-100 ميجابايت)
echo   File size may be large (50-100 MB)
echo.
echo - أول تشغيل قد يستغرق وقتاً أطول
echo   First run may take longer
echo.
echo - تأكد من وجود ملف قاعدة البيانات في نفس المجلد
echo   Ensure database file is in the same folder
echo.

pause
