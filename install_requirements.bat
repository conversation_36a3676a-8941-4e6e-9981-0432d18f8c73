@echo off
chcp 65001 >nul
title تثبيت المكتبات المطلوبة - Install Required Libraries

echo ================================================
echo    تثبيت المكتبات المطلوبة
echo    Installing Required Libraries
echo ================================================
echo.

echo فحص وجود Python...
echo Checking Python installation...
echo.

REM فحص وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: Python غير مثبت أو غير موجود في PATH
    echo Error: Python is not installed or not in PATH
    echo.
    echo يرجى تثبيت Python 3.6 أو أحدث من:
    echo Please install Python 3.6 or newer from:
    echo https://www.python.org/downloads/
    echo.
    pause
    exit /b 1
)

echo تم العثور على Python ✓
python --version
echo.

echo فحص وجود pip...
echo Checking pip installation...
echo.

pip --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: pip غير متوفر
    echo Error: pip is not available
    echo.
    echo يرجى تثبيت pip أو إعادة تثبيت Python
    echo Please install pip or reinstall Python
    echo.
    pause
    exit /b 1
)

echo تم العثور على pip ✓
pip --version
echo.

echo ================================================
echo بدء تثبيت المكتبات...
echo Starting library installation...
echo ================================================
echo.

REM ترقية pip أولاً
echo ترقية pip...
echo Upgrading pip...
python -m pip install --upgrade pip
echo.

REM تثبيت المكتبات الأساسية
echo تثبيت المكتبات الأساسية...
echo Installing basic libraries...
echo.

echo تثبيت reportlab للتقارير PDF...
echo Installing reportlab for PDF reports...
pip install reportlab>=3.6.0
echo.

echo تثبيت matplotlib للرسوم البيانية...
echo Installing matplotlib for charts...
pip install matplotlib>=3.5.0
echo.

echo تثبيت Pillow لمعالجة الصور...
echo Installing Pillow for image processing...
pip install Pillow>=9.0.0
echo.

echo تثبيت openpyxl للتعامل مع Excel...
echo Installing openpyxl for Excel handling...
pip install openpyxl>=3.0.0
echo.

echo تثبيت pandas لتحليل البيانات...
echo Installing pandas for data analysis...
pip install pandas>=1.4.0
echo.

echo تثبيت numpy للعمليات الرياضية...
echo Installing numpy for mathematical operations...
pip install numpy>=1.21.0
echo.

echo تثبيت python-dateutil للتواريخ...
echo Installing python-dateutil for date handling...
pip install python-dateutil>=2.8.0
echo.

echo تثبيت xlsxwriter لكتابة Excel...
echo Installing xlsxwriter for Excel writing...
pip install xlsxwriter>=3.0.0
echo.

REM تثبيت PyInstaller لإنشاء ملف تنفيذي
echo تثبيت PyInstaller لإنشاء ملف تنفيذي...
echo Installing PyInstaller for executable creation...
pip install pyinstaller>=5.0
echo.

echo ================================================
echo فحص التثبيت...
echo Verifying installation...
echo ================================================
echo.

REM فحص المكتبات المثبتة
echo فحص المكتبات المثبتة...
echo Checking installed libraries...
echo.

python -c "import reportlab; print('reportlab ✓')" 2>nul || echo "reportlab ✗"
python -c "import matplotlib; print('matplotlib ✓')" 2>nul || echo "matplotlib ✗"
python -c "import PIL; print('Pillow ✓')" 2>nul || echo "Pillow ✗"
python -c "import openpyxl; print('openpyxl ✓')" 2>nul || echo "openpyxl ✗"
python -c "import pandas; print('pandas ✓')" 2>nul || echo "pandas ✗"
python -c "import numpy; print('numpy ✓')" 2>nul || echo "numpy ✗"
python -c "import dateutil; print('python-dateutil ✓')" 2>nul || echo "python-dateutil ✗"
python -c "import xlsxwriter; print('xlsxwriter ✓')" 2>nul || echo "xlsxwriter ✗"
python -c "import PyInstaller; print('PyInstaller ✓')" 2>nul || echo "PyInstaller ✗"

echo.
echo ================================================
echo اكتمل التثبيت!
echo Installation completed!
echo ================================================
echo.

echo يمكنك الآن تشغيل التطبيق باستخدام:
echo You can now run the application using:
echo.
echo   python main.py
echo.
echo أو استخدام ملف التشغيل:
echo Or use the run file:
echo.
echo   run_app.bat
echo.

pause
