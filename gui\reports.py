import tkinter as tk
from tkinter import ttk, filedialog
import sys
import os
from datetime import datetime

# إضافة المسار الجذر للمشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.helpers import (create_treeview_with_scrollbars, format_date, format_currency, 
                          get_category_name, get_status_name, show_message, export_to_csv)

class ReportsWindow:
    def __init__(self, parent, db_manager, language_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.language_manager = language_manager
        self.current_report_data = []
        self.current_report_type = None
        
        # مسح المحتوى السابق
        for widget in self.parent.winfo_children():
            widget.destroy()
        
        self.create_interface()
    
    def create_interface(self):
        """إنشاء واجهة التقارير"""
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.parent)
        main_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # العنوان
        title_label = ttk.Label(main_frame, 
                               text=self.language_manager.get_text('reports'),
                               font=('Arial', 14, 'bold'))
        title_label.pack(pady=(0, 20))
        
        # إطار اختيار التقارير
        self.create_report_selection(main_frame)
        
        # إطار عرض التقرير
        self.create_report_display(main_frame)
        
        # إطار أزرار التحكم
        self.create_control_buttons(main_frame)
    
    def create_report_selection(self, parent):
        """إنشاء لوحة اختيار التقارير"""
        selection_frame = ttk.LabelFrame(parent, text="اختيار التقرير", padding="15")
        selection_frame.pack(fill='x', pady=(0, 15))
        
        # الصف الأول - التقارير الأساسية
        basic_reports_frame = ttk.Frame(selection_frame)
        basic_reports_frame.pack(fill='x', pady=(0, 15))
        
        ttk.Label(basic_reports_frame, text="التقارير الأساسية:", 
                 font=('Arial', 10, 'bold')).pack(anchor='w', pady=(0, 10))
        
        # أزرار التقارير الأساسية
        basic_buttons_frame = ttk.Frame(basic_reports_frame)
        basic_buttons_frame.pack(fill='x')
        
        ttk.Button(basic_buttons_frame, text="📊 تقرير الاستهلاك العالي", 
                  command=self.generate_high_consumption_report).pack(side='left', padx=(0, 10))
        ttk.Button(basic_buttons_frame, text="💰 تقرير الديون", 
                  command=self.generate_debt_report).pack(side='left', padx=(0, 10))
        ttk.Button(basic_buttons_frame, text="🏢 الحسابات التجارية", 
                  command=self.generate_commercial_report).pack(side='left', padx=(0, 10))
        ttk.Button(basic_buttons_frame, text="🏠 الحسابات المنزلية", 
                  command=self.generate_residential_report).pack(side='left')
        
        # فاصل
        ttk.Separator(selection_frame, orient='horizontal').pack(fill='x', pady=15)
        
        # الصف الثاني - التقارير المتقدمة
        advanced_reports_frame = ttk.Frame(selection_frame)
        advanced_reports_frame.pack(fill='x')
        
        ttk.Label(advanced_reports_frame, text="التقارير المتقدمة:", 
                 font=('Arial', 10, 'bold')).pack(anchor='w', pady=(0, 10))
        
        # أزرار التقارير المتقدمة
        advanced_buttons_frame = ttk.Frame(advanced_reports_frame)
        advanced_buttons_frame.pack(fill='x')
        
        ttk.Button(advanced_buttons_frame, text="🆕 الحسابات الجديدة", 
                  command=self.generate_new_accounts_report).pack(side='left', padx=(0, 10))
        ttk.Button(advanced_buttons_frame, text="📈 تقرير الإحصائيات", 
                  command=self.generate_statistics_report).pack(side='left', padx=(0, 10))
        ttk.Button(advanced_buttons_frame, text="🔒 الحسابات المغلقة", 
                  command=self.generate_closed_accounts_report).pack(side='left', padx=(0, 10))
        ttk.Button(advanced_buttons_frame, text="⚠️ المقاييس المعطلة", 
                  command=self.generate_bad_meters_report).pack(side='left')
    
    def create_report_display(self, parent):
        """إنشاء منطقة عرض التقرير"""
        display_frame = ttk.LabelFrame(parent, text="عرض التقرير", padding="10")
        display_frame.pack(fill='both', expand=True, pady=(0, 15))
        
        # معلومات التقرير
        self.report_info_frame = ttk.Frame(display_frame)
        self.report_info_frame.pack(fill='x', pady=(0, 10))
        
        self.report_title_label = ttk.Label(self.report_info_frame, 
                                           text="اختر تقريراً من الأعلى",
                                           font=('Arial', 12, 'bold'))
        self.report_title_label.pack(side='left')
        
        self.report_count_label = ttk.Label(self.report_info_frame, 
                                           text="",
                                           font=('Arial', 10))
        self.report_count_label.pack(side='right')
        
        # جدول التقرير
        self.create_report_table(display_frame)
    
    def create_report_table(self, parent):
        """إنشاء جدول التقرير"""
        # تعريف الأعمدة الافتراضية
        columns = ['ACCTNO', 'NAME_A', 'ADRESS', 'METER_NO', 'OUTS', 'HOUSE_CODE']
        headings = ['رقم الحساب', 'الاسم', 'العنوان', 'رقم المقياس', 'الدين', 'الصنف']
        
        # إنشاء الجدول
        self.report_tree_frame, self.report_tree = create_treeview_with_scrollbars(
            parent, columns, headings)
        self.report_tree_frame.pack(fill='both', expand=True)
        
        # تكوين الأعمدة
        self.report_tree.column('ACCTNO', width=120, anchor='center')
        self.report_tree.column('NAME_A', width=200, anchor='center')
        self.report_tree.column('ADRESS', width=150, anchor='center')
        self.report_tree.column('METER_NO', width=120, anchor='center')
        self.report_tree.column('OUTS', width=100, anchor='center')
        self.report_tree.column('HOUSE_CODE', width=100, anchor='center')
        
        # ربط الأحداث
        self.report_tree.bind('<Double-1>', self.on_item_double_click)
    
    def create_control_buttons(self, parent):
        """إنشاء أزرار التحكم"""
        buttons_frame = ttk.Frame(parent)
        buttons_frame.pack(fill='x', pady=10)
        
        # أزرار التصدير والطباعة
        ttk.Button(buttons_frame, text="🖨️ طباعة التقرير", 
                  command=self.print_report).pack(side='left', padx=(0, 10))
        ttk.Button(buttons_frame, text="📤 تصدير إلى CSV", 
                  command=self.export_to_csv).pack(side='left', padx=(0, 10))
        ttk.Button(buttons_frame, text="📄 تصدير إلى PDF", 
                  command=self.export_to_pdf).pack(side='left', padx=(0, 10))
        
        # زر تحديث
        ttk.Button(buttons_frame, text="🔄 تحديث التقرير", 
                  command=self.refresh_current_report).pack(side='right')
    
    def generate_high_consumption_report(self):
        """إنشاء تقرير الاستهلاك العالي"""
        try:
            # طلب حد الاستهلاك من المستخدم
            threshold_window = tk.Toplevel(self.parent)
            threshold_window.title("حد الاستهلاك العالي")
            threshold_window.geometry("300x150")
            threshold_window.resizable(False, False)
            
            ttk.Label(threshold_window, text="أدخل حد الاستهلاك العالي:").pack(pady=20)
            
            threshold_var = tk.StringVar(value="1000")
            threshold_entry = ttk.Entry(threshold_window, textvariable=threshold_var, width=20)
            threshold_entry.pack(pady=10)
            
            def generate_report():
                try:
                    threshold = int(threshold_var.get())
                    data = self.db_manager.get_high_consumption_report(threshold)
                    self.display_report("تقرير الاستهلاك العالي", data, "high_consumption")
                    threshold_window.destroy()
                except ValueError:
                    show_message("خطأ", "يرجى إدخال رقم صحيح", "error")
                except Exception as e:
                    show_message("خطأ", f"فشل في إنشاء التقرير: {str(e)}", "error")
            
            ttk.Button(threshold_window, text="إنشاء التقرير", 
                      command=generate_report).pack(pady=10)
            
        except Exception as e:
            show_message("خطأ", f"فشل في إنشاء التقرير: {str(e)}", "error")
    
    def generate_debt_report(self):
        """إنشاء تقرير الديون"""
        try:
            data = self.db_manager.get_debt_report()
            self.display_report("تقرير الديون", data, "debt")
        except Exception as e:
            show_message("خطأ", f"فشل في إنشاء تقرير الديون: {str(e)}", "error")
    
    def generate_commercial_report(self):
        """إنشاء تقرير الحسابات التجارية"""
        try:
            data = self.db_manager.get_commercial_accounts()
            self.display_report("تقرير الحسابات التجارية", data, "commercial")
        except Exception as e:
            show_message("خطأ", f"فشل في إنشاء تقرير الحسابات التجارية: {str(e)}", "error")
    
    def generate_residential_report(self):
        """إنشاء تقرير الحسابات المنزلية"""
        try:
            data = self.db_manager.get_residential_accounts()
            self.display_report("تقرير الحسابات المنزلية", data, "residential")
        except Exception as e:
            show_message("خطأ", f"فشل في إنشاء تقرير الحسابات المنزلية: {str(e)}", "error")
    
    def generate_new_accounts_report(self):
        """إنشاء تقرير الحسابات الجديدة"""
        try:
            # طلب عدد الأيام من المستخدم
            days_window = tk.Toplevel(self.parent)
            days_window.title("الحسابات الجديدة")
            days_window.geometry("300x150")
            days_window.resizable(False, False)
            
            ttk.Label(days_window, text="أدخل عدد الأيام الماضية:").pack(pady=20)
            
            days_var = tk.StringVar(value="30")
            days_entry = ttk.Entry(days_window, textvariable=days_var, width=20)
            days_entry.pack(pady=10)
            
            def generate_report():
                try:
                    days = int(days_var.get())
                    data = self.db_manager.get_new_accounts_report(days)
                    self.display_report(f"الحسابات الجديدة (آخر {days} يوم)", data, "new_accounts")
                    days_window.destroy()
                except ValueError:
                    show_message("خطأ", "يرجى إدخال رقم صحيح", "error")
                except Exception as e:
                    show_message("خطأ", f"فشل في إنشاء التقرير: {str(e)}", "error")
            
            ttk.Button(days_window, text="إنشاء التقرير", 
                      command=generate_report).pack(pady=10)
            
        except Exception as e:
            show_message("خطأ", f"فشل في إنشاء التقرير: {str(e)}", "error")
    
    def generate_statistics_report(self):
        """إنشاء تقرير الإحصائيات"""
        try:
            stats = self.db_manager.get_statistics()
            
            # إنشاء نافذة الإحصائيات
            stats_window = tk.Toplevel(self.parent)
            stats_window.title("تقرير الإحصائيات")
            stats_window.geometry("500x400")
            
            # المحتوى
            content_frame = ttk.Frame(stats_window, padding="20")
            content_frame.pack(fill='both', expand=True)
            
            # العنوان
            title_label = ttk.Label(content_frame, text="تقرير الإحصائيات العامة", 
                                   font=('Arial', 14, 'bold'))
            title_label.pack(pady=(0, 20))
            
            # الإحصائيات
            stats_data = [
                ("إجمالي الحسابات", stats.get('total_accounts', 0)),
                ("الحسابات المغلقة", stats.get('closed_accounts', 0)),
                ("الحسابات المفتوحة", stats.get('total_accounts', 0) - stats.get('closed_accounts', 0)),
                ("إجمالي الديون", f"{stats.get('total_debt', 0):,.0f} دينار"),
                ("الحسابات التجارية", stats.get('commercial_accounts', 0)),
                ("الحسابات المنزلية", stats.get('residential_accounts', 0)),
                ("نسبة الحسابات المغلقة", f"{(stats.get('closed_accounts', 0) / max(stats.get('total_accounts', 1), 1) * 100):.1f}%"),
                ("متوسط الدين لكل حساب", f"{(stats.get('total_debt', 0) / max(stats.get('total_accounts', 1), 1)):,.0f} دينار")
            ]
            
            for i, (label, value) in enumerate(stats_data):
                row_frame = ttk.Frame(content_frame)
                row_frame.pack(fill='x', pady=5)
                
                ttk.Label(row_frame, text=f"{label}:", font=('Arial', 11)).pack(side='left')
                ttk.Label(row_frame, text=str(value), font=('Arial', 11, 'bold')).pack(side='right')
            
            # زر الإغلاق
            ttk.Button(content_frame, text="إغلاق", 
                      command=stats_window.destroy).pack(pady=20)
            
        except Exception as e:
            show_message("خطأ", f"فشل في إنشاء تقرير الإحصائيات: {str(e)}", "error")
    
    def generate_closed_accounts_report(self):
        """إنشاء تقرير الحسابات المغلقة"""
        try:
            data = self.db_manager.filter_by_status(True)  # الحسابات المغلقة
            self.display_report("تقرير الحسابات المغلقة", data, "closed_accounts")
        except Exception as e:
            show_message("خطأ", f"فشل في إنشاء تقرير الحسابات المغلقة: {str(e)}", "error")
    
    def generate_bad_meters_report(self):
        """إنشاء تقرير المقاييس المعطلة"""
        try:
            # البحث عن المقاييس المعطلة (BAD_M_FLAG = 1)
            query = "SELECT * FROM electricity_bills WHERE BAD_M_FLAG = 1"
            data = self.db_manager.execute_query(query)
            self.display_report("تقرير المقاييس المعطلة", data, "bad_meters")
        except Exception as e:
            show_message("خطأ", f"فشل في إنشاء تقرير المقاييس المعطلة: {str(e)}", "error")

    def display_report(self, title, data, report_type):
        """عرض التقرير"""
        self.current_report_data = data
        self.current_report_type = report_type

        # تحديث عنوان التقرير
        self.report_title_label.configure(text=title)
        self.report_count_label.configure(text=f"عدد السجلات: {len(data)}")

        # مسح البيانات السابقة
        for item in self.report_tree.get_children():
            self.report_tree.delete(item)

        # عرض البيانات الجديدة
        for row in data:
            values = [
                str(row.get('ACCTNO', '')),
                str(row.get('NAME_A', '')),
                str(row.get('ADRESS', '')),
                str(row.get('METER_NO', '')),
                format_currency(row.get('OUTS', 0)),
                get_category_name(row.get('HOUSE_CODE', 0), self.language_manager.get_current_language())
            ]
            self.report_tree.insert('', 'end', values=values)

        if not data:
            show_message("معلومات", "لا توجد بيانات لهذا التقرير", "info")

    def on_item_double_click(self, event):
        """عند النقر المزدوج على عنصر"""
        selection = self.report_tree.selection()
        if selection:
            item = self.report_tree.item(selection[0])
            account_no = item['values'][0]
            self.show_customer_details(account_no)

    def show_customer_details(self, account_no):
        """عرض تفاصيل المشترك"""
        try:
            results = self.db_manager.search_by_account(str(account_no))
            if not results:
                show_message("خطأ", "لم يتم العثور على المشترك", "error")
                return

            customer = results[0]

            # إنشاء نافذة التفاصيل
            details_window = tk.Toplevel(self.parent)
            details_window.title(f"تفاصيل المشترك - {customer.get('NAME_A', '')}")
            details_window.geometry("600x500")
            details_window.resizable(False, False)

            # المحتوى
            content_frame = ttk.Frame(details_window, padding="20")
            content_frame.pack(fill='both', expand=True)

            # العنوان
            title_label = ttk.Label(content_frame, text="تفاصيل المشترك",
                                   font=('Arial', 14, 'bold'))
            title_label.pack(pady=(0, 20))

            # البيانات الأساسية
            basic_frame = ttk.LabelFrame(content_frame, text="البيانات الأساسية", padding="10")
            basic_frame.pack(fill='x', pady=(0, 10))

            basic_data = [
                ("رقم الحساب", customer.get('ACCTNO', '')),
                ("الاسم", customer.get('NAME_A', '')),
                ("العنوان", customer.get('ADRESS', '')),
                ("رقم المقياس", customer.get('METER_NO', '')),
                ("الصنف", get_category_name(customer.get('HOUSE_CODE', 0))),
                ("الحالة", get_status_name(customer.get('EVEN_CLOSE', 0)))
            ]

            for i, (label, value) in enumerate(basic_data):
                row = i // 2
                col = (i % 2) * 2

                ttk.Label(basic_frame, text=f"{label}:", font=('Arial', 9)).grid(
                    row=row, column=col, sticky='w', padx=(0, 10), pady=5)
                ttk.Label(basic_frame, text=str(value), font=('Arial', 9, 'bold')).grid(
                    row=row, column=col+1, sticky='w', padx=(0, 30), pady=5)

            # البيانات المالية
            financial_frame = ttk.LabelFrame(content_frame, text="البيانات المالية", padding="10")
            financial_frame.pack(fill='x', pady=(0, 10))

            financial_data = [
                ("الدين الحالي", format_currency(customer.get('OUTS', 0))),
                ("الدين السابق", format_currency(customer.get('BKOUTS', 0))),
                ("المدفوع", format_currency(customer.get('PAYMENT', 0))),
                ("تاريخ الدفع", format_date(customer.get('PAY_DATE', 0))),
                ("تاريخ الفاتورة", format_date(customer.get('BILL_DATE', 0)))
            ]

            for i, (label, value) in enumerate(financial_data):
                row = i // 2
                col = (i % 2) * 2

                ttk.Label(financial_frame, text=f"{label}:", font=('Arial', 9)).grid(
                    row=row, column=col, sticky='w', padx=(0, 10), pady=5)
                ttk.Label(financial_frame, text=str(value), font=('Arial', 9, 'bold')).grid(
                    row=row, column=col+1, sticky='w', padx=(0, 30), pady=5)

            # بيانات القراءات
            readings_frame = ttk.LabelFrame(content_frame, text="بيانات القراءات", padding="10")
            readings_frame.pack(fill='x', pady=(0, 20))

            consumption = customer.get('LAST_READ', 0) - customer.get('PREV_READ', 0)
            readings_data = [
                ("القراءة الحالية", customer.get('LAST_READ', 0)),
                ("القراءة السابقة", customer.get('PREV_READ', 0)),
                ("الاستهلاك", consumption),
                ("تاريخ القراءة الحالية", format_date(customer.get('LAST_DATE', 0))),
                ("تاريخ القراءة السابقة", format_date(customer.get('PREV_DATE', 0)))
            ]

            for i, (label, value) in enumerate(readings_data):
                row = i // 2
                col = (i % 2) * 2

                ttk.Label(readings_frame, text=f"{label}:", font=('Arial', 9)).grid(
                    row=row, column=col, sticky='w', padx=(0, 10), pady=5)
                ttk.Label(readings_frame, text=str(value), font=('Arial', 9, 'bold')).grid(
                    row=row, column=col+1, sticky='w', padx=(0, 30), pady=5)

            # زر الإغلاق
            ttk.Button(content_frame, text="إغلاق",
                      command=details_window.destroy).pack(pady=10)

        except Exception as e:
            show_message("خطأ", f"فشل في عرض التفاصيل: {str(e)}", "error")

    def print_report(self):
        """طباعة التقرير"""
        if not self.current_report_data:
            show_message("تحذير", "لا يوجد تقرير لطباعته", "warning")
            return

        try:
            # إنشاء ملف HTML للطباعة
            html_content = self.generate_html_report()

            # حفظ الملف المؤقت
            import tempfile
            with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False, encoding='utf-8') as f:
                f.write(html_content)
                temp_file = f.name

            # فتح الملف في المتصفح للطباعة
            import webbrowser
            webbrowser.open(f'file://{temp_file}')

            show_message("معلومات", "تم فتح التقرير في المتصفح للطباعة", "info")

        except Exception as e:
            show_message("خطأ", f"فشل في طباعة التقرير: {str(e)}", "error")

    def export_to_csv(self):
        """تصدير التقرير إلى CSV"""
        if not self.current_report_data:
            show_message("تحذير", "لا يوجد تقرير للتصدير", "warning")
            return

        try:
            filename = filedialog.asksaveasfilename(
                defaultextension=".csv",
                filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
                title="حفظ التقرير"
            )

            if filename:
                headers = ['ACCTNO', 'NAME_A', 'ADRESS', 'METER_NO', 'OUTS', 'HOUSE_CODE']
                if export_to_csv(self.current_report_data, filename, headers):
                    show_message("نجح", f"تم تصدير التقرير إلى {filename}", "info")
                else:
                    show_message("خطأ", "فشل في تصدير التقرير", "error")

        except Exception as e:
            show_message("خطأ", f"فشل في التصدير: {str(e)}", "error")

    def export_to_pdf(self):
        """تصدير التقرير إلى PDF"""
        if not self.current_report_data:
            show_message("تحذير", "لا يوجد تقرير للتصدير", "warning")
            return

        try:
            filename = filedialog.asksaveasfilename(
                defaultextension=".pdf",
                filetypes=[("PDF files", "*.pdf"), ("All files", "*.*")],
                title="حفظ التقرير كـ PDF"
            )

            if filename:
                self.create_pdf_report(filename)
                show_message("نجح", f"تم تصدير التقرير إلى {filename}", "info")

        except Exception as e:
            show_message("خطأ", f"فشل في تصدير PDF: {str(e)}", "error")

    def generate_html_report(self):
        """إنشاء تقرير HTML"""
        html = f"""
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <title>{self.report_title_label.cget('text')}</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                h1 {{ text-align: center; color: #2E86AB; }}
                table {{ width: 100%; border-collapse: collapse; margin-top: 20px; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: center; }}
                th {{ background-color: #f2f2f2; font-weight: bold; }}
                .summary {{ background-color: #f9f9f9; padding: 15px; margin-bottom: 20px; }}
            </style>
        </head>
        <body>
            <h1>{self.report_title_label.cget('text')}</h1>
            <div class="summary">
                <p><strong>تاريخ التقرير:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                <p><strong>عدد السجلات:</strong> {len(self.current_report_data)}</p>
            </div>
            <table>
                <thead>
                    <tr>
                        <th>رقم الحساب</th>
                        <th>الاسم</th>
                        <th>العنوان</th>
                        <th>رقم المقياس</th>
                        <th>الدين</th>
                        <th>الصنف</th>
                    </tr>
                </thead>
                <tbody>
        """

        for row in self.current_report_data:
            html += f"""
                    <tr>
                        <td>{row.get('ACCTNO', '')}</td>
                        <td>{row.get('NAME_A', '')}</td>
                        <td>{row.get('ADRESS', '')}</td>
                        <td>{row.get('METER_NO', '')}</td>
                        <td>{format_currency(row.get('OUTS', 0))}</td>
                        <td>{get_category_name(row.get('HOUSE_CODE', 0))}</td>
                    </tr>
            """

        html += """
                </tbody>
            </table>
        </body>
        </html>
        """

        return html

    def create_pdf_report(self, filename):
        """إنشاء تقرير PDF"""
        try:
            from reportlab.lib.pagesizes import letter, A4
            from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
            from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
            from reportlab.lib import colors
            from reportlab.pdfbase import pdfmetrics
            from reportlab.pdfbase.ttfonts import TTFont

            # إنشاء المستند
            doc = SimpleDocTemplate(filename, pagesize=A4)
            story = []

            # الأنماط
            styles = getSampleStyleSheet()
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontSize=18,
                spaceAfter=30,
                alignment=1  # توسيط
            )

            # العنوان
            title = Paragraph(self.report_title_label.cget('text'), title_style)
            story.append(title)
            story.append(Spacer(1, 12))

            # معلومات التقرير
            info_text = f"تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}<br/>عدد السجلات: {len(self.current_report_data)}"
            info = Paragraph(info_text, styles['Normal'])
            story.append(info)
            story.append(Spacer(1, 12))

            # إعداد البيانات للجدول
            data = [['رقم الحساب', 'الاسم', 'العنوان', 'رقم المقياس', 'الدين', 'الصنف']]

            for row in self.current_report_data:
                data.append([
                    str(row.get('ACCTNO', '')),
                    str(row.get('NAME_A', ''))[:20],  # تقصير النص
                    str(row.get('ADRESS', ''))[:15],
                    str(row.get('METER_NO', '')),
                    format_currency(row.get('OUTS', 0)),
                    get_category_name(row.get('HOUSE_CODE', 0))[:10]
                ])

            # إنشاء الجدول
            table = Table(data)
            table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 10),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))

            story.append(table)

            # بناء المستند
            doc.build(story)

        except ImportError:
            show_message("خطأ", "مكتبة ReportLab غير مثبتة. يرجى تثبيتها لتصدير PDF", "error")
        except Exception as e:
            show_message("خطأ", f"فشل في إنشاء PDF: {str(e)}", "error")

    def refresh_current_report(self):
        """تحديث التقرير الحالي"""
        if not self.current_report_type:
            show_message("معلومات", "لا يوجد تقرير لتحديثه", "info")
            return

        # إعادة إنشاء التقرير حسب النوع
        if self.current_report_type == "high_consumption":
            self.generate_high_consumption_report()
        elif self.current_report_type == "debt":
            self.generate_debt_report()
        elif self.current_report_type == "commercial":
            self.generate_commercial_report()
        elif self.current_report_type == "residential":
            self.generate_residential_report()
        elif self.current_report_type == "new_accounts":
            self.generate_new_accounts_report()
        elif self.current_report_type == "closed_accounts":
            self.generate_closed_accounts_report()
        elif self.current_report_type == "bad_meters":
            self.generate_bad_meters_report()
