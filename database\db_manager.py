import sqlite3
import os
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple

class DatabaseManager:
    def __init__(self, db_path: str = "electricity_bills.db"):
        self.db_path = db_path
        self.connection = None
        
    def connect(self):
        """إنشاء اتصال بقاعدة البيانات"""
        try:
            self.connection = sqlite3.connect(self.db_path)
            self.connection.row_factory = sqlite3.Row  # للحصول على النتائج كقاموس
            return True
        except Exception as e:
            print(f"خطأ في الاتصال بقاعدة البيانات: {e}")
            return False
    
    def disconnect(self):
        """قطع الاتصال بقاعدة البيانات"""
        if self.connection:
            self.connection.close()
            self.connection = None
    
    def execute_query(self, query: str, params: tuple = ()) -> List[Dict]:
        """تنفيذ استعلام وإرجاع النتائج"""
        if not self.connection:
            self.connect()
        
        try:
            cursor = self.connection.cursor()
            cursor.execute(query, params)
            
            if query.strip().upper().startswith('SELECT'):
                results = cursor.fetchall()
                return [dict(row) for row in results]
            else:
                self.connection.commit()
                return []
        except Exception as e:
            print(f"خطأ في تنفيذ الاستعلام: {e}")
            return []
    
    def get_all_bills(self, limit: int = 1000) -> List[Dict]:
        """جلب جميع الفواتير"""
        query = f"SELECT * FROM electricity_bills LIMIT {limit}"
        return self.execute_query(query)
    
    def search_by_name(self, name: str) -> List[Dict]:
        """البحث بالاسم"""
        query = "SELECT * FROM electricity_bills WHERE NAME_A LIKE ?"
        return self.execute_query(query, (f"%{name}%",))
    
    def search_by_account(self, account_no: str) -> List[Dict]:
        """البحث برقم الحساب"""
        query = "SELECT * FROM electricity_bills WHERE ACCTNO LIKE ?"
        return self.execute_query(query, (f"%{account_no}%",))
    
    def search_by_meter(self, meter_no: str) -> List[Dict]:
        """البحث برقم المقياس"""
        query = "SELECT * FROM electricity_bills WHERE METER_NO LIKE ?"
        return self.execute_query(query, (f"%{meter_no}%",))
    
    def multi_search(self, search_term: str) -> List[Dict]:
        """البحث المتعدد في الاسم ورقم الحساب ورقم المقياس"""
        query = """
        SELECT * FROM electricity_bills 
        WHERE NAME_A LIKE ? OR 
              CAST(ACCTNO AS TEXT) LIKE ? OR 
              CAST(METER_NO AS TEXT) LIKE ?
        """
        search_pattern = f"%{search_term}%"
        return self.execute_query(query, (search_pattern, search_pattern, search_pattern))
    
    def filter_by_debt(self, min_debt: float = 0) -> List[Dict]:
        """فلترة حسب الديون"""
        query = "SELECT * FROM electricity_bills WHERE OUTS > ?"
        return self.execute_query(query, (min_debt,))
    
    def filter_by_category(self, house_code: int) -> List[Dict]:
        """فلترة حسب الصنف"""
        query = "SELECT * FROM electricity_bills WHERE HOUSE_CODE = ?"
        return self.execute_query(query, (house_code,))
    
    def filter_by_status(self, is_closed: bool) -> List[Dict]:
        """فلترة حسب حالة الإغلاق"""
        status = 1 if is_closed else 0
        query = "SELECT * FROM electricity_bills WHERE EVEN_CLOSE = ?"
        return self.execute_query(query, (status,))
    
    def get_high_consumption_report(self, threshold: int = 1000) -> List[Dict]:
        """تقرير الاستهلاك العالي"""
        query = """
        SELECT *, (LAST_READ - PREV_READ) as consumption 
        FROM electricity_bills 
        WHERE (LAST_READ - PREV_READ) > ?
        ORDER BY consumption DESC
        """
        return self.execute_query(query, (threshold,))
    
    def get_debt_report(self) -> List[Dict]:
        """تقرير الديون"""
        query = """
        SELECT * FROM electricity_bills 
        WHERE OUTS > 0 
        ORDER BY OUTS DESC
        """
        return self.execute_query(query)
    
    def get_commercial_accounts(self) -> List[Dict]:
        """الحسابات التجارية"""
        query = "SELECT * FROM electricity_bills WHERE HOUSE_CODE = 96"
        return self.execute_query(query)
    
    def get_residential_accounts(self) -> List[Dict]:
        """الحسابات المنزلية"""
        query = "SELECT * FROM electricity_bills WHERE HOUSE_CODE = 15"
        return self.execute_query(query)
    
    def get_new_accounts_report(self, days: int = 30) -> List[Dict]:
        """تقرير الحسابات الجديدة"""
        # نحتاج لتحويل التاريخ من صيغة YYYYMMDD
        current_date = datetime.now()
        date_threshold = int(current_date.strftime("%Y%m%d")) - (days * 100)  # تقريبي
        
        query = "SELECT * FROM electricity_bills WHERE BILL_DATE > ?"
        return self.execute_query(query, (date_threshold,))
    
    def update_account_name(self, account_no: int, new_name: str) -> bool:
        """تحديث اسم الحساب"""
        query = "UPDATE electricity_bills SET NAME_A = ? WHERE ACCTNO = ?"
        try:
            self.execute_query(query, (new_name, account_no))
            return True
        except:
            return False
    
    def update_account_category(self, account_no: int, new_category: int) -> bool:
        """تحديث صنف الحساب"""
        query = "UPDATE electricity_bills SET HOUSE_CODE = ? WHERE ACCTNO = ?"
        try:
            self.execute_query(query, (new_category, account_no))
            return True
        except:
            return False
    
    def close_account(self, account_no: int) -> bool:
        """إغلاق الحساب"""
        query = "UPDATE electricity_bills SET EVEN_CLOSE = 1 WHERE ACCTNO = ?"
        try:
            self.execute_query(query, (account_no,))
            return True
        except:
            return False
    
    def replace_meter(self, account_no: int, new_meter_no: float) -> bool:
        """استبدال المقياس"""
        query = "UPDATE electricity_bills SET METER_NO = ? WHERE ACCTNO = ?"
        try:
            self.execute_query(query, (new_meter_no, account_no))
            return True
        except:
            return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """إحصائيات عامة"""
        stats = {}
        
        # إجمالي الحسابات
        total_accounts = self.execute_query("SELECT COUNT(*) as count FROM electricity_bills")
        stats['total_accounts'] = total_accounts[0]['count'] if total_accounts else 0
        
        # الحسابات المغلقة
        closed_accounts = self.execute_query("SELECT COUNT(*) as count FROM electricity_bills WHERE EVEN_CLOSE = 1")
        stats['closed_accounts'] = closed_accounts[0]['count'] if closed_accounts else 0
        
        # إجمالي الديون
        total_debt = self.execute_query("SELECT SUM(OUTS) as total FROM electricity_bills")
        stats['total_debt'] = total_debt[0]['total'] if total_debt and total_debt[0]['total'] else 0
        
        # الحسابات التجارية
        commercial = self.execute_query("SELECT COUNT(*) as count FROM electricity_bills WHERE HOUSE_CODE = 96")
        stats['commercial_accounts'] = commercial[0]['count'] if commercial else 0
        
        # الحسابات المنزلية
        residential = self.execute_query("SELECT COUNT(*) as count FROM electricity_bills WHERE HOUSE_CODE = 15")
        stats['residential_accounts'] = residential[0]['count'] if residential else 0
        
        return stats
