#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة فواتير الكهرباء
Electricity Bills Management System

تطبيق شامل لإدارة فواتير الكهرباء يشمل:
- عرض وفلترة بيانات المشتركين
- البحث المتقدم والاستفسارات
- إدارة المعاملات (استبدال مقياس، إغلاق حساب، تغيير بيانات)
- إنشاء التقارير المفصلة
- مقارنة الاستهلاكات والضائعات
- واجهة ثنائية اللغة (عربي/إنجليزي)

المطور: Augment Agent
التاريخ: 2024
الإصدار: 1.0
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox
import traceback

# إضافة مسار المشروع إلى sys.path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def check_dependencies():
    """فحص المكتبات المطلوبة"""
    required_modules = [
        'tkinter',
        'sqlite3',
        'datetime',
        'csv',
        'json'
    ]
    
    optional_modules = [
        'matplotlib',
        'reportlab',
        'openpyxl',
        'pandas'
    ]
    
    missing_required = []
    missing_optional = []
    
    # فحص المكتبات الأساسية
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_required.append(module)
    
    # فحص المكتبات الاختيارية
    for module in optional_modules:
        try:
            __import__(module)
        except ImportError:
            missing_optional.append(module)
    
    if missing_required:
        error_msg = f"المكتبات التالية مطلوبة ولكنها غير مثبتة:\n{', '.join(missing_required)}"
        messagebox.showerror("خطأ في المكتبات", error_msg)
        return False
    
    if missing_optional:
        warning_msg = f"المكتبات التالية اختيارية ولكنها غير مثبتة:\n{', '.join(missing_optional)}\n\nبعض الميزات قد لا تعمل بشكل كامل."
        messagebox.showwarning("تحذير", warning_msg)
    
    return True

def check_database():
    """فحص وجود قاعدة البيانات"""
    db_path = os.path.join(project_root, "electricity_bills.db")
    
    if not os.path.exists(db_path):
        error_msg = f"ملف قاعدة البيانات غير موجود:\n{db_path}\n\nيرجى التأكد من وجود الملف في نفس مجلد البرنامج."
        messagebox.showerror("خطأ في قاعدة البيانات", error_msg)
        return False
    
    # فحص إمكانية الوصول لقاعدة البيانات
    try:
        import sqlite3
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        conn.close()
        
        if not tables:
            error_msg = "قاعدة البيانات فارغة أو تالفة.\nيرجى التأكد من صحة ملف قاعدة البيانات."
            messagebox.showerror("خطأ في قاعدة البيانات", error_msg)
            return False
        
        return True
        
    except Exception as e:
        error_msg = f"فشل في الاتصال بقاعدة البيانات:\n{str(e)}"
        messagebox.showerror("خطأ في قاعدة البيانات", error_msg)
        return False

def setup_error_handling():
    """إعداد معالجة الأخطاء العامة"""
    def handle_exception(exc_type, exc_value, exc_traceback):
        if issubclass(exc_type, KeyboardInterrupt):
            sys.__excepthook__(exc_type, exc_value, exc_traceback)
            return
        
        error_msg = f"حدث خطأ غير متوقع:\n\n{exc_type.__name__}: {exc_value}\n\nتفاصيل الخطأ:\n{''.join(traceback.format_tb(exc_traceback))}"
        
        # كتابة الخطأ في ملف log
        try:
            with open("error_log.txt", "a", encoding="utf-8") as f:
                f.write(f"\n{'='*50}\n")
                f.write(f"التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"نوع الخطأ: {exc_type.__name__}\n")
                f.write(f"رسالة الخطأ: {exc_value}\n")
                f.write(f"تفاصيل الخطأ:\n{''.join(traceback.format_tb(exc_traceback))}\n")
        except:
            pass
        
        messagebox.showerror("خطأ في البرنامج", error_msg)
    
    sys.excepthook = handle_exception

def create_splash_screen():
    """إنشاء شاشة البداية"""
    splash = tk.Toplevel()
    splash.title("نظام إدارة فواتير الكهرباء")
    splash.geometry("400x300")
    splash.resizable(False, False)
    
    # توسيط النافذة
    splash.update_idletasks()
    x = (splash.winfo_screenwidth() // 2) - (400 // 2)
    y = (splash.winfo_screenheight() // 2) - (300 // 2)
    splash.geometry(f"400x300+{x}+{y}")
    
    # إزالة شريط العنوان
    splash.overrideredirect(True)
    
    # الخلفية
    splash.configure(bg='#2E86AB')
    
    # المحتوى
    main_frame = tk.Frame(splash, bg='#2E86AB', padx=40, pady=40)
    main_frame.pack(fill='both', expand=True)
    
    # العنوان
    title_label = tk.Label(main_frame, 
                          text="نظام إدارة فواتير الكهرباء",
                          font=('Arial', 18, 'bold'),
                          fg='white',
                          bg='#2E86AB')
    title_label.pack(pady=(20, 10))
    
    # العنوان الفرعي
    subtitle_label = tk.Label(main_frame,
                             text="Electricity Bills Management System",
                             font=('Arial', 12),
                             fg='#F5F5F5',
                             bg='#2E86AB')
    subtitle_label.pack(pady=(0, 30))
    
    # شريط التحميل
    progress_frame = tk.Frame(main_frame, bg='#2E86AB')
    progress_frame.pack(pady=20)
    
    progress_label = tk.Label(progress_frame,
                             text="جاري التحميل...",
                             font=('Arial', 10),
                             fg='white',
                             bg='#2E86AB')
    progress_label.pack()
    
    # معلومات الإصدار
    version_label = tk.Label(main_frame,
                            text="الإصدار 1.0 - 2024",
                            font=('Arial', 9),
                            fg='#F5F5F5',
                            bg='#2E86AB')
    version_label.pack(side='bottom', pady=(30, 0))
    
    # تحديث النافذة
    splash.update()
    
    return splash

def main():
    """الدالة الرئيسية للتطبيق"""
    try:
        # إعداد معالجة الأخطاء
        setup_error_handling()
        
        # إنشاء النافذة الجذر (مخفية)
        root = tk.Tk()
        root.withdraw()  # إخفاء النافذة الرئيسية مؤقتاً
        
        # إنشاء شاشة البداية
        splash = create_splash_screen()
        
        # فحص المتطلبات
        splash.update()
        
        # فحص المكتبات
        if not check_dependencies():
            splash.destroy()
            root.destroy()
            return
        
        # فحص قاعدة البيانات
        if not check_database():
            splash.destroy()
            root.destroy()
            return
        
        # تحديث شاشة البداية
        splash.update()
        
        # استيراد الواجهة الرئيسية
        try:
            from gui.main_window import MainWindow
        except ImportError as e:
            messagebox.showerror("خطأ في الاستيراد", f"فشل في استيراد الواجهة الرئيسية:\n{str(e)}")
            splash.destroy()
            root.destroy()
            return
        
        # إغلاق شاشة البداية
        splash.destroy()
        
        # إظهار النافذة الرئيسية
        root.deiconify()
        root.withdraw()  # إخفاؤها مرة أخرى لأن MainWindow ستنشئ نافذتها الخاصة
        
        # إنشاء وتشغيل التطبيق الرئيسي
        app = MainWindow()
        app.run()
        
    except Exception as e:
        error_msg = f"فشل في تشغيل التطبيق:\n{str(e)}\n\nتفاصيل الخطأ:\n{traceback.format_exc()}"
        messagebox.showerror("خطأ في التشغيل", error_msg)
        
        # كتابة الخطأ في ملف log
        try:
            from datetime import datetime
            with open("startup_error_log.txt", "a", encoding="utf-8") as f:
                f.write(f"\n{'='*50}\n")
                f.write(f"التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"خطأ في التشغيل: {str(e)}\n")
                f.write(f"تفاصيل الخطأ:\n{traceback.format_exc()}\n")
        except:
            pass
    
    finally:
        # تنظيف الموارد
        try:
            root.quit()
            root.destroy()
        except:
            pass

if __name__ == "__main__":
    # التأكد من أن التطبيق يعمل في البيئة الصحيحة
    if sys.version_info < (3, 6):
        messagebox.showerror("خطأ في الإصدار", 
                           "هذا التطبيق يتطلب Python 3.6 أو أحدث.\n"
                           f"الإصدار الحالي: {sys.version}")
        sys.exit(1)
    
    # تشغيل التطبيق
    main()
