import tkinter as tk
from tkinter import ttk
import sys
import os

# إضافة المسار الجذر للمشروع
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.helpers import (create_treeview_with_scrollbars, format_date, format_currency, 
                          get_category_name, get_status_name, show_message, validate_account_number,
                          validate_meter_number, validate_name)

class TransactionsWindow:
    def __init__(self, parent, db_manager, language_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.language_manager = language_manager
        self.current_data = []
        self.selected_account = None
        
        # مسح المحتوى السابق
        for widget in self.parent.winfo_children():
            widget.destroy()
        
        self.create_interface()
        self.load_data()
    
    def create_interface(self):
        """إنشاء واجهة المعاملات"""
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.parent)
        main_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # العنوان
        title_label = ttk.Label(main_frame, 
                               text=self.language_manager.get_text('transactions'),
                               font=('Arial', 14, 'bold'))
        title_label.pack(pady=(0, 20))
        
        # إطار البحث والفلترة
        self.create_search_panel(main_frame)
        
        # إطار الجدول
        self.create_data_table(main_frame)
        
        # إطار المعاملات
        self.create_transaction_panel(main_frame)
    
    def create_search_panel(self, parent):
        """إنشاء لوحة البحث والفلترة"""
        search_frame = ttk.LabelFrame(parent, text="البحث والفلترة", padding="10")
        search_frame.pack(fill='x', pady=(0, 10))
        
        # الصف الأول - البحث
        search_row = ttk.Frame(search_frame)
        search_row.pack(fill='x', pady=(0, 10))
        
        ttk.Label(search_row, text="البحث:").pack(side='left', padx=(0, 5))
        
        self.search_var = tk.StringVar()
        search_entry = ttk.Entry(search_row, textvariable=self.search_var, width=30)
        search_entry.pack(side='left', padx=(0, 10))
        search_entry.bind('<KeyRelease>', self.on_search_change)
        
        ttk.Button(search_row, text="🔍 بحث", 
                  command=self.search_data).pack(side='left', padx=(0, 5))
        ttk.Button(search_row, text="🔄 تحديث", 
                  command=self.load_data).pack(side='left')
        
        # الصف الثاني - الفلاتر
        filter_row = ttk.Frame(search_frame)
        filter_row.pack(fill='x')
        
        # فلتر نوع المعاملة
        ttk.Label(filter_row, text="نوع المعاملة:").pack(side='left', padx=(0, 5))
        self.transaction_type_var = tk.StringVar(value="الكل")
        type_combo = ttk.Combobox(filter_row, textvariable=self.transaction_type_var,
                                 values=["الكل", "3", "96", "97", "98"],
                                 state="readonly", width=15)
        type_combo.pack(side='left', padx=(0, 20))
        type_combo.bind('<<ComboboxSelected>>', self.apply_filters)
        
        # فلتر الحالة
        ttk.Label(filter_row, text="الحالة:").pack(side='left', padx=(0, 5))
        self.status_filter_var = tk.StringVar(value="الكل")
        status_combo = ttk.Combobox(filter_row, textvariable=self.status_filter_var,
                                   values=["الكل", "مفتوح", "مغلق"],
                                   state="readonly", width=15)
        status_combo.pack(side='left', padx=(0, 20))
        status_combo.bind('<<ComboboxSelected>>', self.apply_filters)
        
        # زر إعادة تعيين
        ttk.Button(filter_row, text="🔄 إعادة تعيين", 
                  command=self.reset_filters).pack(side='right')
    
    def create_data_table(self, parent):
        """إنشاء جدول البيانات"""
        table_frame = ttk.LabelFrame(parent, text="بيانات المشتركين", padding="5")
        table_frame.pack(fill='both', expand=True, pady=(0, 10))
        
        # تعريف الأعمدة
        columns = ['ACCTNO', 'NAME_A', 'ADRESS', 'METER_NO', 'HOUSE_CODE', 'EVEN_CLOSE']
        headings = ['رقم الحساب', 'الاسم', 'العنوان', 'رقم المقياس', 'الصنف', 'الحالة']
        
        # إنشاء الجدول
        self.tree_frame, self.tree = create_treeview_with_scrollbars(table_frame, columns, headings)
        self.tree_frame.pack(fill='both', expand=True)
        
        # تكوين الأعمدة
        self.tree.column('ACCTNO', width=120, anchor='center')
        self.tree.column('NAME_A', width=200, anchor='center')
        self.tree.column('ADRESS', width=150, anchor='center')
        self.tree.column('METER_NO', width=120, anchor='center')
        self.tree.column('HOUSE_CODE', width=100, anchor='center')
        self.tree.column('EVEN_CLOSE', width=80, anchor='center')
        
        # ربط الأحداث
        self.tree.bind('<ButtonRelease-1>', self.on_item_select)
        self.tree.bind('<Double-1>', self.on_item_double_click)
    
    def create_transaction_panel(self, parent):
        """إنشاء لوحة المعاملات"""
        transaction_frame = ttk.LabelFrame(parent, text="إدارة المعاملات", padding="15")
        transaction_frame.pack(fill='x')
        
        # معلومات المشترك المحدد
        self.create_selected_info(transaction_frame)
        
        # أنواع المعاملات
        self.create_transaction_types(transaction_frame)
        
        # أزرار التحكم
        self.create_control_buttons(transaction_frame)
    
    def create_selected_info(self, parent):
        """إنشاء معلومات المشترك المحدد"""
        info_frame = ttk.LabelFrame(parent, text="المشترك المحدد", padding="10")
        info_frame.pack(fill='x', pady=(0, 15))
        
        self.selected_info_frame = ttk.Frame(info_frame)
        self.selected_info_frame.pack(fill='x')
        
        # رسالة افتراضية
        self.no_selection_label = ttk.Label(self.selected_info_frame, 
                                           text="اختر مشتركاً من الجدول أعلاه لإجراء المعاملات",
                                           font=('Arial', 10, 'italic'))
        self.no_selection_label.pack(pady=10)
    
    def create_transaction_types(self, parent):
        """إنشاء أنواع المعاملات"""
        types_frame = ttk.LabelFrame(parent, text="أنواع المعاملات", padding="10")
        types_frame.pack(fill='x', pady=(0, 15))
        
        # نوع المعاملة
        type_selection_frame = ttk.Frame(types_frame)
        type_selection_frame.pack(fill='x', pady=(0, 15))
        
        ttk.Label(type_selection_frame, text="نوع المعاملة:", 
                 font=('Arial', 10, 'bold')).pack(side='left', padx=(0, 10))
        
        self.transaction_type = tk.StringVar(value="meter_replacement")
        
        # أزرار اختيار نوع المعاملة
        ttk.Radiobutton(type_selection_frame, text="استبدال مقياس", 
                       variable=self.transaction_type, value="meter_replacement",
                       command=self.on_transaction_type_change).pack(side='left', padx=(0, 15))
        ttk.Radiobutton(type_selection_frame, text="إغلاق حساب", 
                       variable=self.transaction_type, value="account_closure",
                       command=self.on_transaction_type_change).pack(side='left', padx=(0, 15))
        ttk.Radiobutton(type_selection_frame, text="تغيير الاسم", 
                       variable=self.transaction_type, value="name_change",
                       command=self.on_transaction_type_change).pack(side='left', padx=(0, 15))
        ttk.Radiobutton(type_selection_frame, text="تغيير الصنف", 
                       variable=self.transaction_type, value="category_change",
                       command=self.on_transaction_type_change).pack(side='left')
        
        # إطار تفاصيل المعاملة
        self.transaction_details_frame = ttk.Frame(types_frame)
        self.transaction_details_frame.pack(fill='x')
        
        # عرض تفاصيل المعاملة الافتراضية
        self.show_transaction_details()
    
    def create_control_buttons(self, parent):
        """إنشاء أزرار التحكم"""
        buttons_frame = ttk.Frame(parent)
        buttons_frame.pack(fill='x', pady=10)
        
        # أزرار المعاملات
        ttk.Button(buttons_frame, text="💾 حفظ المعاملة", 
                  command=self.save_transaction, 
                  style='Primary.TButton').pack(side='left', padx=(0, 10))
        
        ttk.Button(buttons_frame, text="✏️ تعديل", 
                  command=self.edit_transaction).pack(side='left', padx=(0, 10))
        
        ttk.Button(buttons_frame, text="🗑️ حذف", 
                  command=self.delete_transaction).pack(side='left', padx=(0, 10))
        
        ttk.Button(buttons_frame, text="➕ إضافة جديد", 
                  command=self.add_new_record).pack(side='left', padx=(0, 10))
        
        # زر مسح النموذج
        ttk.Button(buttons_frame, text="🔄 مسح النموذج", 
                  command=self.clear_form).pack(side='right')
    
    def load_data(self):
        """تحميل البيانات"""
        try:
            self.current_data = self.db_manager.get_all_bills(limit=3000)
            self.display_data(self.current_data)
        except Exception as e:
            show_message("خطأ", f"فشل في تحميل البيانات: {str(e)}", "error")
    
    def display_data(self, data):
        """عرض البيانات في الجدول"""
        # مسح البيانات السابقة
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # إضافة البيانات الجديدة
        for row in data:
            values = [
                str(row.get('ACCTNO', '')),
                str(row.get('NAME_A', '')),
                str(row.get('ADRESS', '')),
                str(row.get('METER_NO', '')),
                get_category_name(row.get('HOUSE_CODE', 0), self.language_manager.get_current_language()),
                get_status_name(row.get('EVEN_CLOSE', 0), self.language_manager.get_current_language())
            ]
            self.tree.insert('', 'end', values=values)
    
    def on_search_change(self, event):
        """عند تغيير نص البحث"""
        self.parent.after(500, self.search_data)
    
    def search_data(self):
        """البحث في البيانات"""
        search_term = self.search_var.get().strip()
        
        if not search_term:
            self.display_data(self.current_data)
            return
        
        try:
            results = self.db_manager.multi_search(search_term)
            self.display_data(results)
        except Exception as e:
            show_message("خطأ", f"فشل في البحث: {str(e)}", "error")
    
    def apply_filters(self, event=None):
        """تطبيق الفلاتر"""
        try:
            filtered_data = self.current_data.copy()
            
            # فلتر نوع المعاملة (حسب HOUSE_CODE)
            transaction_type = self.transaction_type_var.get()
            if transaction_type != "الكل":
                type_code = int(transaction_type)
                filtered_data = [row for row in filtered_data 
                               if row.get('HOUSE_CODE') == type_code]
            
            # فلتر الحالة
            status = self.status_filter_var.get()
            if status != "الكل":
                status_code = 0 if status == "مفتوح" else 1
                filtered_data = [row for row in filtered_data 
                               if row.get('EVEN_CLOSE') == status_code]
            
            self.display_data(filtered_data)
            
        except Exception as e:
            show_message("خطأ", f"فشل في تطبيق الفلاتر: {str(e)}", "error")
    
    def reset_filters(self):
        """إعادة تعيين الفلاتر"""
        self.transaction_type_var.set("الكل")
        self.status_filter_var.set("الكل")
        self.search_var.set("")
        self.display_data(self.current_data)
    
    def on_item_select(self, event):
        """عند اختيار عنصر من الجدول"""
        selection = self.tree.selection()
        if selection:
            item = self.tree.item(selection[0])
            account_no = item['values'][0]
            self.show_selected_customer_info(account_no)
    
    def on_item_double_click(self, event):
        """عند النقر المزدوج على عنصر"""
        selection = self.tree.selection()
        if selection:
            item = self.tree.item(selection[0])
            account_no = item['values'][0]
            self.show_customer_details(account_no)

    def show_selected_customer_info(self, account_no):
        """عرض معلومات المشترك المحدد"""
        try:
            results = self.db_manager.search_by_account(str(account_no))
            if not results:
                return

            customer = results[0]
            self.selected_account = customer

            # مسح المعلومات السابقة
            for widget in self.selected_info_frame.winfo_children():
                widget.destroy()

            # عرض المعلومات الأساسية
            info_grid = ttk.Frame(self.selected_info_frame)
            info_grid.pack(fill='x')

            basic_info = [
                ("رقم الحساب", customer.get('ACCTNO', '')),
                ("الاسم", customer.get('NAME_A', '')),
                ("العنوان", customer.get('ADRESS', '')),
                ("رقم المقياس", customer.get('METER_NO', '')),
                ("الصنف", get_category_name(customer.get('HOUSE_CODE', 0))),
                ("الحالة", get_status_name(customer.get('EVEN_CLOSE', 0)))
            ]

            for i, (label, value) in enumerate(basic_info):
                row = i // 3
                col = (i % 3) * 2

                ttk.Label(info_grid, text=f"{label}:", font=('Arial', 9)).grid(
                    row=row, column=col, sticky='w', padx=(0, 5), pady=2)
                ttk.Label(info_grid, text=str(value), font=('Arial', 9, 'bold')).grid(
                    row=row, column=col+1, sticky='w', padx=(0, 20), pady=2)

        except Exception as e:
            show_message("خطأ", f"فشل في عرض معلومات المشترك: {str(e)}", "error")

    def show_customer_details(self, account_no):
        """عرض تفاصيل المشترك في نافذة منفصلة"""
        try:
            results = self.db_manager.search_by_account(str(account_no))
            if not results:
                show_message("خطأ", "لم يتم العثور على المشترك", "error")
                return

            customer = results[0]

            # إنشاء نافذة التفاصيل
            details_window = tk.Toplevel(self.parent)
            details_window.title(f"تفاصيل المشترك - {customer.get('NAME_A', '')}")
            details_window.geometry("600x400")
            details_window.resizable(False, False)

            # المحتوى
            content_frame = ttk.Frame(details_window, padding="20")
            content_frame.pack(fill='both', expand=True)

            # العنوان
            title_label = ttk.Label(content_frame, text="تفاصيل المشترك",
                                   font=('Arial', 14, 'bold'))
            title_label.pack(pady=(0, 20))

            # البيانات
            details_data = [
                ("رقم الحساب", customer.get('ACCTNO', '')),
                ("الاسم", customer.get('NAME_A', '')),
                ("العنوان", customer.get('ADRESS', '')),
                ("رقم المقياس", customer.get('METER_NO', '')),
                ("الصنف", get_category_name(customer.get('HOUSE_CODE', 0))),
                ("الحالة", get_status_name(customer.get('EVEN_CLOSE', 0))),
                ("الدين", format_currency(customer.get('OUTS', 0))),
                ("القراءة الحالية", customer.get('LAST_READ', 0)),
                ("القراءة السابقة", customer.get('PREV_READ', 0)),
                ("الاستهلاك", customer.get('LAST_READ', 0) - customer.get('PREV_READ', 0))
            ]

            for i, (label, value) in enumerate(details_data):
                row = i // 2
                col = (i % 2) * 2

                ttk.Label(content_frame, text=f"{label}:", font=('Arial', 10)).grid(
                    row=row, column=col, sticky='w', padx=(0, 10), pady=5)
                ttk.Label(content_frame, text=str(value), font=('Arial', 10, 'bold')).grid(
                    row=row, column=col+1, sticky='w', padx=(0, 30), pady=5)

            # زر الإغلاق
            ttk.Button(content_frame, text="إغلاق",
                      command=details_window.destroy).grid(row=len(details_data)//2 + 1,
                                                          column=1, pady=20)

        except Exception as e:
            show_message("خطأ", f"فشل في عرض التفاصيل: {str(e)}", "error")

    def on_transaction_type_change(self):
        """عند تغيير نوع المعاملة"""
        self.show_transaction_details()

    def show_transaction_details(self):
        """عرض تفاصيل المعاملة حسب النوع"""
        # مسح التفاصيل السابقة
        for widget in self.transaction_details_frame.winfo_children():
            widget.destroy()

        transaction_type = self.transaction_type.get()

        if transaction_type == "meter_replacement":
            self.show_meter_replacement_form()
        elif transaction_type == "account_closure":
            self.show_account_closure_form()
        elif transaction_type == "name_change":
            self.show_name_change_form()
        elif transaction_type == "category_change":
            self.show_category_change_form()

    def show_meter_replacement_form(self):
        """عرض نموذج استبدال المقياس"""
        form_frame = ttk.LabelFrame(self.transaction_details_frame,
                                   text="استبدال المقياس", padding="10")
        form_frame.pack(fill='x', pady=10)

        # رقم المقياس الجديد
        meter_frame = ttk.Frame(form_frame)
        meter_frame.pack(fill='x', pady=5)

        ttk.Label(meter_frame, text="رقم المقياس الجديد:", width=20).pack(side='left')
        self.new_meter_var = tk.StringVar()
        ttk.Entry(meter_frame, textvariable=self.new_meter_var, width=20).pack(side='left', padx=(10, 0))

        # ملاحظات
        notes_frame = ttk.Frame(form_frame)
        notes_frame.pack(fill='x', pady=5)

        ttk.Label(notes_frame, text="ملاحظات:", width=20).pack(side='left')
        self.meter_notes_var = tk.StringVar()
        ttk.Entry(notes_frame, textvariable=self.meter_notes_var, width=40).pack(side='left', padx=(10, 0))

    def show_account_closure_form(self):
        """عرض نموذج إغلاق الحساب"""
        form_frame = ttk.LabelFrame(self.transaction_details_frame,
                                   text="إغلاق الحساب", padding="10")
        form_frame.pack(fill='x', pady=10)

        # سبب الإغلاق
        reason_frame = ttk.Frame(form_frame)
        reason_frame.pack(fill='x', pady=5)

        ttk.Label(reason_frame, text="سبب الإغلاق:", width=20).pack(side='left')
        self.closure_reason_var = tk.StringVar()
        reason_combo = ttk.Combobox(reason_frame, textvariable=self.closure_reason_var,
                                   values=["طلب المشترك", "عدم الدفع", "مخالفة", "أخرى"],
                                   width=20)
        reason_combo.pack(side='left', padx=(10, 0))

        # تاريخ الإغلاق
        date_frame = ttk.Frame(form_frame)
        date_frame.pack(fill='x', pady=5)

        ttk.Label(date_frame, text="تاريخ الإغلاق:", width=20).pack(side='left')
        self.closure_date_var = tk.StringVar()
        ttk.Entry(date_frame, textvariable=self.closure_date_var, width=20).pack(side='left', padx=(10, 0))

        # تأكيد الإغلاق
        confirm_frame = ttk.Frame(form_frame)
        confirm_frame.pack(fill='x', pady=5)

        self.confirm_closure_var = tk.BooleanVar()
        ttk.Checkbutton(confirm_frame, text="أؤكد إغلاق هذا الحساب",
                       variable=self.confirm_closure_var).pack(side='left')

    def show_name_change_form(self):
        """عرض نموذج تغيير الاسم"""
        form_frame = ttk.LabelFrame(self.transaction_details_frame,
                                   text="تغيير الاسم", padding="10")
        form_frame.pack(fill='x', pady=10)

        # الاسم الحالي
        current_frame = ttk.Frame(form_frame)
        current_frame.pack(fill='x', pady=5)

        ttk.Label(current_frame, text="الاسم الحالي:", width=20).pack(side='left')
        current_name = self.selected_account.get('NAME_A', '') if self.selected_account else ''
        ttk.Label(current_frame, text=current_name, font=('Arial', 9, 'bold')).pack(side='left', padx=(10, 0))

        # الاسم الجديد
        new_frame = ttk.Frame(form_frame)
        new_frame.pack(fill='x', pady=5)

        ttk.Label(new_frame, text="الاسم الجديد:", width=20).pack(side='left')
        self.new_name_var = tk.StringVar()
        ttk.Entry(new_frame, textvariable=self.new_name_var, width=30).pack(side='left', padx=(10, 0))

        # سبب التغيير
        reason_frame = ttk.Frame(form_frame)
        reason_frame.pack(fill='x', pady=5)

        ttk.Label(reason_frame, text="سبب التغيير:", width=20).pack(side='left')
        self.name_change_reason_var = tk.StringVar()
        ttk.Entry(reason_frame, textvariable=self.name_change_reason_var, width=40).pack(side='left', padx=(10, 0))

    def show_category_change_form(self):
        """عرض نموذج تغيير الصنف"""
        form_frame = ttk.LabelFrame(self.transaction_details_frame,
                                   text="تغيير الصنف", padding="10")
        form_frame.pack(fill='x', pady=10)

        # الصنف الحالي
        current_frame = ttk.Frame(form_frame)
        current_frame.pack(fill='x', pady=5)

        ttk.Label(current_frame, text="الصنف الحالي:", width=20).pack(side='left')
        current_category = get_category_name(self.selected_account.get('HOUSE_CODE', 0)) if self.selected_account else ''
        ttk.Label(current_frame, text=current_category, font=('Arial', 9, 'bold')).pack(side='left', padx=(10, 0))

        # الصنف الجديد
        new_frame = ttk.Frame(form_frame)
        new_frame.pack(fill='x', pady=5)

        ttk.Label(new_frame, text="الصنف الجديد:", width=20).pack(side='left')
        self.new_category_var = tk.StringVar()
        category_combo = ttk.Combobox(new_frame, textvariable=self.new_category_var,
                                     values=["منزلي (15)", "تجاري (96)", "صناعي (97)", "حكومي (98)"],
                                     width=20)
        category_combo.pack(side='left', padx=(10, 0))

        # سبب التغيير
        reason_frame = ttk.Frame(form_frame)
        reason_frame.pack(fill='x', pady=5)

        ttk.Label(reason_frame, text="سبب التغيير:", width=20).pack(side='left')
        self.category_change_reason_var = tk.StringVar()
        ttk.Entry(reason_frame, textvariable=self.category_change_reason_var, width=40).pack(side='left', padx=(10, 0))

    def save_transaction(self):
        """حفظ المعاملة"""
        if not self.selected_account:
            show_message("تحذير", "يرجى اختيار مشترك أولاً", "warning")
            return

        transaction_type = self.transaction_type.get()
        account_no = self.selected_account.get('ACCTNO')

        try:
            if transaction_type == "meter_replacement":
                self.save_meter_replacement(account_no)
            elif transaction_type == "account_closure":
                self.save_account_closure(account_no)
            elif transaction_type == "name_change":
                self.save_name_change(account_no)
            elif transaction_type == "category_change":
                self.save_category_change(account_no)

        except Exception as e:
            show_message("خطأ", f"فشل في حفظ المعاملة: {str(e)}", "error")

    def save_meter_replacement(self, account_no):
        """حفظ معاملة استبدال المقياس"""
        new_meter = self.new_meter_var.get().strip()

        if not new_meter:
            show_message("تحذير", "يرجى إدخال رقم المقياس الجديد", "warning")
            return

        if not validate_meter_number(new_meter):
            show_message("خطأ", "رقم المقياس غير صحيح", "error")
            return

        # تأكيد العملية
        if not show_message("تأكيد", f"هل تريد استبدال المقياس برقم {new_meter}؟", "question"):
            return

        # تنفيذ العملية
        if self.db_manager.replace_meter(account_no, float(new_meter)):
            show_message("نجح", "تم استبدال المقياس بنجاح", "info")
            self.load_data()  # تحديث البيانات
            self.clear_form()
        else:
            show_message("خطأ", "فشل في استبدال المقياس", "error")

    def save_account_closure(self, account_no):
        """حفظ معاملة إغلاق الحساب"""
        if not hasattr(self, 'confirm_closure_var') or not self.confirm_closure_var.get():
            show_message("تحذير", "يرجى تأكيد إغلاق الحساب", "warning")
            return

        reason = self.closure_reason_var.get()
        if not reason:
            show_message("تحذير", "يرجى اختيار سبب الإغلاق", "warning")
            return

        # تأكيد العملية
        if not show_message("تأكيد", "هل تريد إغلاق هذا الحساب نهائياً؟", "question"):
            return

        # تنفيذ العملية
        if self.db_manager.close_account(account_no):
            show_message("نجح", "تم إغلاق الحساب بنجاح", "info")
            self.load_data()  # تحديث البيانات
            self.clear_form()
        else:
            show_message("خطأ", "فشل في إغلاق الحساب", "error")

    def save_name_change(self, account_no):
        """حفظ معاملة تغيير الاسم"""
        new_name = self.new_name_var.get().strip()

        if not new_name:
            show_message("تحذير", "يرجى إدخال الاسم الجديد", "warning")
            return

        if not validate_name(new_name):
            show_message("خطأ", "الاسم غير صحيح", "error")
            return

        # تأكيد العملية
        if not show_message("تأكيد", f"هل تريد تغيير الاسم إلى '{new_name}'؟", "question"):
            return

        # تنفيذ العملية
        if self.db_manager.update_account_name(account_no, new_name):
            show_message("نجح", "تم تغيير الاسم بنجاح", "info")
            self.load_data()  # تحديث البيانات
            self.clear_form()
        else:
            show_message("خطأ", "فشل في تغيير الاسم", "error")

    def save_category_change(self, account_no):
        """حفظ معاملة تغيير الصنف"""
        new_category_text = self.new_category_var.get()

        if not new_category_text:
            show_message("تحذير", "يرجى اختيار الصنف الجديد", "warning")
            return

        # استخراج رقم الصنف من النص
        category_codes = {
            "منزلي (15)": 15,
            "تجاري (96)": 96,
            "صناعي (97)": 97,
            "حكومي (98)": 98
        }

        new_category_code = category_codes.get(new_category_text)
        if not new_category_code:
            show_message("خطأ", "صنف غير صحيح", "error")
            return

        # تأكيد العملية
        if not show_message("تأكيد", f"هل تريد تغيير الصنف إلى '{new_category_text}'؟", "question"):
            return

        # تنفيذ العملية
        if self.db_manager.update_account_category(account_no, new_category_code):
            show_message("نجح", "تم تغيير الصنف بنجاح", "info")
            self.load_data()  # تحديث البيانات
            self.clear_form()
        else:
            show_message("خطأ", "فشل في تغيير الصنف", "error")

    def edit_transaction(self):
        """تعديل المعاملة"""
        if not self.selected_account:
            show_message("تحذير", "يرجى اختيار مشترك أولاً", "warning")
            return

        # ملء النموذج بالبيانات الحالية
        transaction_type = self.transaction_type.get()

        if transaction_type == "name_change":
            if hasattr(self, 'new_name_var'):
                self.new_name_var.set(self.selected_account.get('NAME_A', ''))
        elif transaction_type == "category_change":
            if hasattr(self, 'new_category_var'):
                current_code = self.selected_account.get('HOUSE_CODE', 0)
                category_map = {15: "منزلي (15)", 96: "تجاري (96)", 97: "صناعي (97)", 98: "حكومي (98)"}
                self.new_category_var.set(category_map.get(current_code, ""))

        show_message("معلومات", "تم تحميل البيانات الحالية للتعديل", "info")

    def delete_transaction(self):
        """حذف المعاملة (في الواقع إغلاق الحساب)"""
        if not self.selected_account:
            show_message("تحذير", "يرجى اختيار مشترك أولاً", "warning")
            return

        account_no = self.selected_account.get('ACCTNO')
        customer_name = self.selected_account.get('NAME_A', '')

        # تأكيد الحذف
        if not show_message("تأكيد الحذف",
                           f"هل تريد حذف (إغلاق) حساب '{customer_name}' نهائياً؟\nهذا الإجراء لا يمكن التراجع عنه!",
                           "question"):
            return

        try:
            if self.db_manager.close_account(account_no):
                show_message("نجح", "تم حذف الحساب بنجاح", "info")
                self.load_data()  # تحديث البيانات
                self.clear_form()
            else:
                show_message("خطأ", "فشل في حذف الحساب", "error")

        except Exception as e:
            show_message("خطأ", f"فشل في حذف الحساب: {str(e)}", "error")

    def add_new_record(self):
        """إضافة سجل جديد"""
        show_message("معلومات", "سيتم إضافة وظيفة إضافة سجل جديد قريباً", "info")

    def clear_form(self):
        """مسح النموذج"""
        # مسح متغيرات النموذج
        if hasattr(self, 'new_meter_var'):
            self.new_meter_var.set("")
        if hasattr(self, 'meter_notes_var'):
            self.meter_notes_var.set("")
        if hasattr(self, 'closure_reason_var'):
            self.closure_reason_var.set("")
        if hasattr(self, 'closure_date_var'):
            self.closure_date_var.set("")
        if hasattr(self, 'confirm_closure_var'):
            self.confirm_closure_var.set(False)
        if hasattr(self, 'new_name_var'):
            self.new_name_var.set("")
        if hasattr(self, 'name_change_reason_var'):
            self.name_change_reason_var.set("")
        if hasattr(self, 'new_category_var'):
            self.new_category_var.set("")
        if hasattr(self, 'category_change_reason_var'):
            self.category_change_reason_var.set("")

        # مسح اختيار المشترك
        self.selected_account = None

        # مسح معلومات المشترك المحدد
        for widget in self.selected_info_frame.winfo_children():
            widget.destroy()

        self.no_selection_label = ttk.Label(self.selected_info_frame,
                                           text="اختر مشتركاً من الجدول أعلاه لإجراء المعاملات",
                                           font=('Arial', 10, 'italic'))
        self.no_selection_label.pack(pady=10)

        # إعادة عرض تفاصيل المعاملة
        self.show_transaction_details()
